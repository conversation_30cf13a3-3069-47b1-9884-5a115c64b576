@import "tailwindcss";

:root {
	--background: #fff8e1;
	--foreground: #4a2c2a;

	/* Spiritual Theme Colors */
	--spiritual-orange: #ff6b35;
	--spiritual-saffron: #ff8c42;
	--spiritual-gold: #ffd700;
	--spiritual-deep-orange: #e55100;
	--spiritual-cream: #fff8e1;
	--spiritual-light-orange: #ffcc80;
}

@theme inline {
	--color-background: var(--background);
	--color-foreground: var(--foreground);
	--font-sans: var(--font-geist-sans);
	--font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
	:root {
		--background: #2d1b1b;
		--foreground: #fff8e1;
	}
}

body {
	background: var(--background);
	color: var(--foreground);
	font-family: Arial, Helvetica, sans-serif;
}

/* Spiritual Theme Classes */
.spiritual-gradient {
	background: linear-gradient(135deg, #ff6b35 0%, #ff8c42 50%, #ffd700 100%);
}

.spiritual-gradient-light {
	background: linear-gradient(135deg, #fff8e1 0%, #ffcc80 50%, #ffe0b2 100%);
}

.divine-glow {
	box-shadow: 0 0 20px rgba(255, 107, 53, 0.3);
}

.sacred-text {
	text-shadow: 1px 1px 2px rgba(255, 140, 66, 0.3);
}
