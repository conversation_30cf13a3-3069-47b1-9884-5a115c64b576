{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/shreeji/marketplace/src/lib/supabase.ts"], "sourcesContent": ["// Simple data types for our marketplace\nexport interface Product {\n\tid: string;\n\tname: string;\n\tdescription: string;\n\tprice: number;\n\timage_url?: string;\n\tcreated_at: string;\n}\n\nexport interface CustomerOrder {\n\tid: string;\n\tcustomerName: string;\n\taddress: string;\n\tphoneNo: string;\n\tpinCode: string;\n\tproductName: string;\n\tproductPrice: number;\n\tquantity: number;\n\ttotalPrice: number;\n\torderDate: string;\n\tstatus: \"pending\" | \"completed\" | \"cancelled\";\n}\n\n// Simple in-memory storage (you can replace this with a file or simple database later)\nlet products: Product[] = [\n\t{\n\t\tid: \"1\",\n\t\tname: \"Sample Product\",\n\t\tdescription: \"This is a sample product to get you started\",\n\t\tprice: 29.99,\n\t\timage_url:\n\t\t\t\"https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400\",\n\t\tcreated_at: new Date().toISOString(),\n\t},\n];\n\nlet orders: CustomerOrder[] = [];\n\n// Product functions\nexport const getProducts = (): Product[] => {\n\treturn products;\n};\n\nexport const addProduct = (\n\tproduct: Omit<Product, \"id\" | \"created_at\">\n): Product => {\n\tconst newProduct: Product = {\n\t\t...product,\n\t\tid: Date.now().toString(),\n\t\tcreated_at: new Date().toISOString(),\n\t};\n\tproducts.push(newProduct);\n\treturn newProduct;\n};\n\nexport const getProductById = (id: string): Product | undefined => {\n\treturn products.find((p) => p.id === id);\n};\n\n// Order functions\nexport const addOrder = (\n\torder: Omit<CustomerOrder, \"id\" | \"orderDate\">\n): CustomerOrder => {\n\tconst newOrder: CustomerOrder = {\n\t\t...order,\n\t\tid: Date.now().toString(),\n\t\torderDate: new Date().toISOString(),\n\t};\n\torders.push(newOrder);\n\n\t// Log order details to console for easy viewing\n\tconsole.log(\"🛒 NEW ORDER RECEIVED:\");\n\tconsole.log(\"Customer:\", newOrder.customerName);\n\tconsole.log(\"Phone:\", newOrder.phoneNo);\n\tconsole.log(\"Address:\", newOrder.address);\n\tconsole.log(\"Pin Code:\", newOrder.pinCode);\n\tconsole.log(\"Product:\", newOrder.productName);\n\tconsole.log(\"Price:\", `$${newOrder.productPrice}`);\n\tconsole.log(\"Quantity:\", newOrder.quantity);\n\tconsole.log(\"Total:\", `$${newOrder.totalPrice}`);\n\tconsole.log(\"Date:\", new Date(newOrder.orderDate).toLocaleString());\n\tconsole.log(\"-------------------\");\n\n\treturn newOrder;\n};\n\nexport const getOrders = (): CustomerOrder[] => {\n\treturn orders;\n};\n"], "names": [], "mappings": "AAAA,wCAAwC;;;;;;;;;;;;;AAwBxC,uFAAuF;AACvF,IAAI,WAAsB;IACzB;QACC,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,WACC;QACD,YAAY,IAAI,OAAO,WAAW;IACnC;CACA;AAED,IAAI,SAA0B,EAAE;AAGzB,MAAM,cAAc;IAC1B,OAAO;AACR;AAEO,MAAM,aAAa,CACzB;IAEA,MAAM,aAAsB;QAC3B,GAAG,OAAO;QACV,IAAI,KAAK,GAAG,GAAG,QAAQ;QACvB,YAAY,IAAI,OAAO,WAAW;IACnC;IACA,SAAS,IAAI,CAAC;IACd,OAAO;AACR;AAEO,MAAM,iBAAiB,CAAC;IAC9B,OAAO,SAAS,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;AACtC;AAGO,MAAM,WAAW,CACvB;IAEA,MAAM,WAA0B;QAC/B,GAAG,KAAK;QACR,IAAI,KAAK,GAAG,GAAG,QAAQ;QACvB,WAAW,IAAI,OAAO,WAAW;IAClC;IACA,OAAO,IAAI,CAAC;IAEZ,gDAAgD;IAChD,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,aAAa,SAAS,YAAY;IAC9C,QAAQ,GAAG,CAAC,UAAU,SAAS,OAAO;IACtC,QAAQ,GAAG,CAAC,YAAY,SAAS,OAAO;IACxC,QAAQ,GAAG,CAAC,aAAa,SAAS,OAAO;IACzC,QAAQ,GAAG,CAAC,YAAY,SAAS,WAAW;IAC5C,QAAQ,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,SAAS,YAAY,EAAE;IACjD,QAAQ,GAAG,CAAC,aAAa,SAAS,QAAQ;IAC1C,QAAQ,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;IAC/C,QAAQ,GAAG,CAAC,SAAS,IAAI,KAAK,SAAS,SAAS,EAAE,cAAc;IAChE,QAAQ,GAAG,CAAC;IAEZ,OAAO;AACR;AAEO,MAAM,YAAY;IACxB,OAAO;AACR", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/shreeji/marketplace/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { getProducts, Product } from \"@/lib/supabase\";\nimport Link from \"next/link\";\n\nexport default function Home() {\n\tconst [products, setProducts] = useState<Product[]>([]);\n\tconst [loading, setLoading] = useState(true);\n\tconst [searchTerm, setSearchTerm] = useState(\"\");\n\n\tuseEffect(() => {\n\t\tfetchProducts();\n\t}, []);\n\n\tconst fetchProducts = () => {\n\t\ttry {\n\t\t\tconst allProducts = getProducts();\n\t\t\tsetProducts(allProducts);\n\t\t} catch (error) {\n\t\t\tconsole.error(\"Error fetching products:\", error);\n\t\t} finally {\n\t\t\tsetLoading(false);\n\t\t}\n\t};\n\n\tconst filteredProducts = products.filter(\n\t\t(product) =>\n\t\t\tproduct.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n\t\t\tproduct.description?.toLowerCase().includes(searchTerm.toLowerCase())\n\t);\n\n\treturn (\n\t\t<div className=\"space-y-8\">\n\t\t\t{/* Hero Section */}\n\t\t\t<div className=\"text-center py-16 bg-gradient-to-br from-orange-50 to-yellow-50 rounded-2xl shadow-xl border-2 border-orange-200 divine-glow\">\n\t\t\t\t<div className=\"mb-6\">\n\t\t\t\t\t<h1 className=\"text-5xl font-bold text-orange-800 mb-4 sacred-text\">\n\t\t\t\t\t\t🙏 Welcome to Shri Hit Store 🙏\n\t\t\t\t\t</h1>\n\t\t\t\t\t<p className=\"text-xl text-orange-700 mb-2\">\n\t\t\t\t\t\tDivine Spiritual Items from Sacred Vrindavan\n\t\t\t\t\t</p>\n\t\t\t\t\t<p className=\"text-lg text-orange-600\">\n\t\t\t\t\t\t✨ For Our Beloved Bhakts ✨\n\t\t\t\t\t</p>\n\t\t\t\t</div>\n\n\t\t\t\t<div className=\"flex justify-center space-x-4\">\n\t\t\t\t\t<Link\n\t\t\t\t\t\thref=\"/products\"\n\t\t\t\t\t\tclassName=\"spiritual-gradient text-white px-8 py-4 rounded-full hover:scale-105 transform transition-all duration-300 font-bold text-lg shadow-lg divine-glow\"\n\t\t\t\t\t>\n\t\t\t\t\t\t🛍️ Shop Sacred Items\n\t\t\t\t\t</Link>\n\t\t\t\t</div>\n\n\t\t\t\t{/* WhatsApp Info */}\n\t\t\t\t<div className=\"mt-8 text-center\">\n\t\t\t\t\t<div className=\"bg-gradient-to-r from-green-100 to-green-50 border-2 border-green-300 rounded-xl p-6 max-w-2xl mx-auto\">\n\t\t\t\t\t\t<h3 className=\"text-lg font-bold text-green-800 mb-2\">\n\t\t\t\t\t\t\t📱 Easy WhatsApp Ordering\n\t\t\t\t\t\t</h3>\n\t\t\t\t\t\t<p className=\"text-green-700 mb-3\">\n\t\t\t\t\t\t\tNo online payments needed! Browse items, fill details, and\n\t\t\t\t\t\t\tcomplete your order via WhatsApp.\n\t\t\t\t\t\t</p>\n\t\t\t\t\t\t<Link\n\t\t\t\t\t\t\thref=\"/how-to-order\"\n\t\t\t\t\t\t\tclassName=\"text-green-600 hover:text-green-500 font-semibold underline\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\tLearn how it works →\n\t\t\t\t\t\t</Link>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\n\t\t\t{/* Search */}\n\t\t\t<div className=\"max-w-md mx-auto\">\n\t\t\t\t<div className=\"relative\">\n\t\t\t\t\t<input\n\t\t\t\t\t\ttype=\"text\"\n\t\t\t\t\t\tplaceholder=\"🔍 Search spiritual items...\"\n\t\t\t\t\t\tvalue={searchTerm}\n\t\t\t\t\t\tonChange={(e) => setSearchTerm(e.target.value)}\n\t\t\t\t\t\tclassName=\"w-full px-6 py-3 border-2 border-orange-300 rounded-full focus:outline-none focus:ring-2 focus:ring-orange-500 text-orange-900 bg-white shadow-lg font-medium\"\n\t\t\t\t\t/>\n\t\t\t\t</div>\n\t\t\t</div>\n\n\t\t\t{/* Our Products */}\n\t\t\t<div>\n\t\t\t\t<h2 className=\"text-3xl font-bold text-orange-800 mb-8 text-center sacred-text\">\n\t\t\t\t\t🕉️ Our Sacred Collection 🕉️\n\t\t\t\t</h2>\n\n\t\t\t\t{loading ? (\n\t\t\t\t\t<div className=\"text-center py-12\">\n\t\t\t\t\t\t<div className=\"text-orange-600 text-lg\">\n\t\t\t\t\t\t\t🙏 Loading sacred items...\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t) : filteredProducts.length === 0 ? (\n\t\t\t\t\t<div className=\"text-center py-12\">\n\t\t\t\t\t\t<div className=\"text-orange-600 text-lg\">\n\t\t\t\t\t\t\t{searchTerm\n\t\t\t\t\t\t\t\t? \"🔍 No sacred items found matching your search.\"\n\t\t\t\t\t\t\t\t: \"📿 No sacred items available yet.\"}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t) : (\n\t\t\t\t\t<div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n\t\t\t\t\t\t{filteredProducts.map((product) => (\n\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\tkey={product.id}\n\t\t\t\t\t\t\t\tclassName=\"bg-gradient-to-br from-white to-orange-50 rounded-2xl shadow-lg border-2 border-orange-200 hover:shadow-xl transition-all duration-300 hover:scale-105 divine-glow\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{product.image_url && (\n\t\t\t\t\t\t\t\t\t<img\n\t\t\t\t\t\t\t\t\t\tsrc={product.image_url}\n\t\t\t\t\t\t\t\t\t\talt={product.name}\n\t\t\t\t\t\t\t\t\t\tclassName=\"w-full h-48 object-cover rounded-t-2xl\"\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t<div className=\"p-6\">\n\t\t\t\t\t\t\t\t\t<h3 className=\"font-bold text-orange-800 mb-3 text-lg sacred-text\">\n\t\t\t\t\t\t\t\t\t\t{product.name}\n\t\t\t\t\t\t\t\t\t</h3>\n\t\t\t\t\t\t\t\t\t<p className=\"text-orange-700 text-sm mb-4 line-clamp-2\">\n\t\t\t\t\t\t\t\t\t\t{product.description}\n\t\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t\t\t<div className=\"flex justify-between items-center\">\n\t\t\t\t\t\t\t\t\t\t<span className=\"text-xl font-bold text-orange-600\">\n\t\t\t\t\t\t\t\t\t\t\t₹{product.price}\n\t\t\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t\t\t\t<Link\n\t\t\t\t\t\t\t\t\t\t\thref={`/products/${product.id}`}\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"spiritual-gradient text-white px-4 py-2 rounded-full text-sm hover:scale-105 transform transition-all duration-200 font-semibold shadow-md\"\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\t🛍️ View\n\t\t\t\t\t\t\t\t\t\t</Link>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t))}\n\t\t\t\t\t</div>\n\t\t\t\t)}\n\t\t\t</div>\n\t\t</div>\n\t);\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iNAAQ,EAAY,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,iNAAQ,EAAC;IAE7C,IAAA,kNAAS,EAAC;QACT;IACD,GAAG,EAAE;IAEL,MAAM,gBAAgB;QACrB,IAAI;YACH,MAAM,cAAc,IAAA,qIAAW;YAC/B,YAAY;QACb,EAAE,OAAO,OAAO;YACf,QAAQ,KAAK,CAAC,4BAA4B;QAC3C,SAAU;YACT,WAAW;QACZ;IACD;IAEA,MAAM,mBAAmB,SAAS,MAAM,CACvC,CAAC,UACA,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC1D,QAAQ,WAAW,EAAE,cAAc,SAAS,WAAW,WAAW;IAGpE,qBACC,8OAAC;QAAI,WAAU;;0BAEd,8OAAC;gBAAI,WAAU;;kCACd,8OAAC;wBAAI,WAAU;;0CACd,8OAAC;gCAAG,WAAU;0CAAsD;;;;;;0CAGpE,8OAAC;gCAAE,WAAU;0CAA+B;;;;;;0CAG5C,8OAAC;gCAAE,WAAU;0CAA0B;;;;;;;;;;;;kCAKxC,8OAAC;wBAAI,WAAU;kCACd,cAAA,8OAAC,uKAAI;4BACJ,MAAK;4BACL,WAAU;sCACV;;;;;;;;;;;kCAMF,8OAAC;wBAAI,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;;8CACd,8OAAC;oCAAG,WAAU;8CAAwC;;;;;;8CAGtD,8OAAC;oCAAE,WAAU;8CAAsB;;;;;;8CAInC,8OAAC,uKAAI;oCACJ,MAAK;oCACL,WAAU;8CACV;;;;;;;;;;;;;;;;;;;;;;;0BAQJ,8OAAC;gBAAI,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;8BACd,cAAA,8OAAC;wBACA,MAAK;wBACL,aAAY;wBACZ,OAAO;wBACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wBAC7C,WAAU;;;;;;;;;;;;;;;;0BAMb,8OAAC;;kCACA,8OAAC;wBAAG,WAAU;kCAAkE;;;;;;oBAI/E,wBACA,8OAAC;wBAAI,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCAA0B;;;;;;;;;;+BAIvC,iBAAiB,MAAM,KAAK,kBAC/B,8OAAC;wBAAI,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACb,aACE,mDACA;;;;;;;;;;6CAIL,8OAAC;wBAAI,WAAU;kCACb,iBAAiB,GAAG,CAAC,CAAC,wBACtB,8OAAC;gCAEA,WAAU;;oCAET,QAAQ,SAAS,kBACjB,8OAAC;wCACA,KAAK,QAAQ,SAAS;wCACtB,KAAK,QAAQ,IAAI;wCACjB,WAAU;;;;;;kDAGZ,8OAAC;wCAAI,WAAU;;0DACd,8OAAC;gDAAG,WAAU;0DACZ,QAAQ,IAAI;;;;;;0DAEd,8OAAC;gDAAE,WAAU;0DACX,QAAQ,WAAW;;;;;;0DAErB,8OAAC;gDAAI,WAAU;;kEACd,8OAAC;wDAAK,WAAU;;4DAAoC;4DACjD,QAAQ,KAAK;;;;;;;kEAEhB,8OAAC,uKAAI;wDACJ,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;wDAC/B,WAAU;kEACV;;;;;;;;;;;;;;;;;;;+BAxBE,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;AAoCvB", "debugId": null}}]}