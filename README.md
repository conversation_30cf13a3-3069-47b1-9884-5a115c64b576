# 🛒 Simple Marketplace

A clean, minimalistic online marketplace built with Next.js and Supabase. Perfect for small businesses wanting to sell products online without complexity.

## ✨ Features

- 🔐 **User Authentication** - Simple email/password signup and login
- 📦 **Product Management** - Easy product creation with images
- 🔍 **Product Search** - Find products quickly with search functionality
- 🛒 **Simple Ordering** - One-click product purchasing
- 📱 **Responsive Design** - Works perfectly on mobile and desktop
- ⚡ **Fast & Modern** - Built with Next.js 15 and Tailwind CSS

## 🚀 Quick Start

1. **Clone and Install**

   ```bash
   git clone <your-repo>
   cd marketplace
   npm install
   ```

2. **Set up Supabase** (see `SETUP-GUIDE.md` for detailed steps)

   - Create a Supabase project
   - Copy your credentials to `.env.local`
   - Run the database schema

3. **Start Development**
   ```bash
   npm run dev
   ```

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── auth/              # Authentication page
│   ├── products/          # Product listing and details
│   ├── add-product/       # Add new products
│   ├── orders/            # Order management
│   └── layout.tsx         # Root layout
├── components/            # Reusable components
│   ├── Auth.tsx          # Login/signup form
│   └── Navbar.tsx        # Navigation bar
└── lib/
    └── supabase.ts       # Database configuration
```

## 🛠 Tech Stack

- **Frontend**: Next.js 15, React 19, Tailwind CSS
- **Backend**: Supabase (PostgreSQL + Auth)
- **Deployment**: Vercel (recommended)
- **Language**: TypeScript

## 📋 Database Schema

Simple 2-table structure:

- **products** - Store product information
- **orders** - Track customer orders
- **users** - Handled by Supabase Auth

## 🎨 Customization

- **Colors**: Edit `src/app/globals.css`
- **Branding**: Update `src/components/Navbar.tsx`
- **Content**: Modify page components in `src/app/`

## 🚀 Deployment

1. Push to GitHub
2. Connect to Vercel
3. Add environment variables
4. Deploy!

See `SETUP-GUIDE.md` for detailed deployment instructions.

## 💡 Future Enhancements

- Payment integration (Stripe)
- Inventory management
- Product categories
- Email notifications
- Admin dashboard
- Product reviews

## 📞 Support

Need help? Check the `SETUP-GUIDE.md` for troubleshooting tips!
