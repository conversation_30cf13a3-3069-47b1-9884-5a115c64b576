"use client";

import { useState, useEffect } from "react";
import { getOrders, CustomerOrder } from "@/lib/supabase";

export default function OrdersPage() {
	const [orders, setOrders] = useState<CustomerOrder[]>([]);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		fetchOrders();
	}, []);

	const fetchOrders = () => {
		try {
			const allOrders = getOrders();
			setOrders(allOrders);
		} catch (error) {
			console.error("Error fetching orders:", error);
		} finally {
			setLoading(false);
		}
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case "completed":
				return "bg-green-100 text-green-800 border border-green-300";
			case "pending":
				return "bg-orange-100 text-orange-800 border border-orange-300";
			case "cancelled":
				return "bg-red-100 text-red-800 border border-red-300";
			default:
				return "bg-orange-50 text-orange-700 border border-orange-200";
		}
	};

	return (
		<div className="space-y-8">
			<div className="text-center">
				<h1 className="text-4xl font-bold text-orange-800 sacred-text">
					📋 Customer Orders 📋
				</h1>
				<p className="text-lg text-orange-600 mt-2">
					Sacred item orders from devotees
				</p>
			</div>

			{loading ? (
				<div className="text-center py-12">
					<div className="text-orange-600 text-lg">🙏 Loading orders...</div>
				</div>
			) : orders.length === 0 ? (
				<div className="text-center py-12">
					<div className="text-orange-600 text-lg mb-4">
						📿 No orders received yet.
					</div>
				</div>
			) : (
				<div className="space-y-4">
					{orders.map((order) => (
						<div
							key={order.id}
							className="bg-gradient-to-br from-white to-orange-50 rounded-2xl shadow-xl border-2 border-orange-200 p-8 divine-glow"
						>
							<div className="grid md:grid-cols-2 gap-8">
								{/* Customer Details */}
								<div>
									<h3 className="text-xl font-bold text-orange-800 mb-4 sacred-text">
										👤 Devotee Details
									</h3>
									<div className="space-y-3 text-sm">
										<p className="text-orange-700">
											<span className="font-bold text-orange-800">Name:</span>{" "}
											{order.customerName}
										</p>
										<p className="text-orange-700">
											<span className="font-bold text-orange-800">Phone:</span>{" "}
											{order.phoneNo}
										</p>
										<p className="text-orange-700">
											<span className="font-bold text-orange-800">
												Address:
											</span>{" "}
											{order.address}
										</p>
										<p className="text-orange-700">
											<span className="font-bold text-orange-800">
												Pin Code:
											</span>{" "}
											{order.pinCode}
										</p>
									</div>
								</div>

								{/* Order Details */}
								<div>
									<h3 className="text-xl font-bold text-orange-800 mb-4 sacred-text">
										📿 Sacred Order Details
									</h3>
									<div className="space-y-3 text-sm">
										<p className="text-orange-700">
											<span className="font-bold text-orange-800">
												Sacred Item:
											</span>{" "}
											{order.productName}
										</p>
										<p className="text-orange-700">
											<span className="font-bold text-orange-800">Price:</span>{" "}
											₹{order.productPrice}
										</p>
										<p className="text-orange-700">
											<span className="font-bold text-orange-800">
												Quantity:
											</span>{" "}
											{order.quantity}
										</p>
										<p className="text-orange-700">
											<span className="font-bold text-orange-800">Total:</span>{" "}
											<span className="text-xl font-bold text-orange-600">
												₹{order.totalPrice}
											</span>
										</p>
										<p className="text-orange-700">
											<span className="font-bold text-orange-800">
												Order Date:
											</span>{" "}
											{new Date(order.orderDate).toLocaleDateString()}
										</p>
									</div>
								</div>
							</div>

							<div className="mt-6 pt-4 border-t-2 border-orange-200 flex justify-between items-center">
								<span
									className={`px-4 py-2 rounded-full text-sm font-bold ${getStatusColor(
										order.status
									)}`}
								>
									{order.status.charAt(0).toUpperCase() + order.status.slice(1)}
								</span>

								<div className="text-sm font-semibold text-orange-600">
									Order ID: SH{order.id.slice(-6)}
								</div>
							</div>
						</div>
					))}
				</div>
			)}
		</div>
	);
}
