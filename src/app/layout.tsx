import type { Metadata } from "next";
import "./globals.css";
import Navbar from "@/components/Navbar";

export const metadata: Metadata = {
	title: "Radha Nam - Sacred Items from Vrindavan",
	description:
		"Divine spiritual items and sacred products from Vrindavan for devotees",
	icons: {
		icon: "/Radha Nam Logo.jpg",
		shortcut: "/Radha Nam Logo.jpg",
		apple: "/Radha Nam Logo.jpg",
	},
};

export default function RootLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	return (
		<html lang="en">
			<body className="min-h-screen spiritual-gradient-light">
				<Navbar />
				<main className="container mx-auto px-4 py-8">{children}</main>
			</body>
		</html>
	);
}
