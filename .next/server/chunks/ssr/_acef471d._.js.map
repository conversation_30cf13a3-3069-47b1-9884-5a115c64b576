{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/shreeji/marketplace/src/lib/supabase.ts"], "sourcesContent": ["// Simple data types for our marketplace\nexport interface Product {\n\tid: string;\n\tname: string;\n\tdescription: string;\n\tprice: number;\n\timage_url?: string;\n\tcreated_at: string;\n}\n\nexport interface CustomerOrder {\n\tid: string;\n\tcustomerName: string;\n\taddress: string;\n\tphoneNo: string;\n\tpinCode: string;\n\tproductName: string;\n\tproductPrice: number;\n\tquantity: number;\n\ttotalPrice: number;\n\torderDate: string;\n\tstatus: \"pending\" | \"completed\" | \"cancelled\";\n}\n\n// Simple in-memory storage (you can replace this with a file or simple database later)\nlet products: Product[] = [\n\t{\n\t\tid: \"1\",\n\t\tname: \"Sample Product\",\n\t\tdescription: \"This is a sample product to get you started\",\n\t\tprice: 29.99,\n\t\timage_url:\n\t\t\t\"https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400\",\n\t\tcreated_at: new Date().toISOString(),\n\t},\n];\n\nlet orders: CustomerOrder[] = [];\n\n// Product functions\nexport const getProducts = (): Product[] => {\n\treturn products;\n};\n\nexport const addProduct = (\n\tproduct: Omit<Product, \"id\" | \"created_at\">\n): Product => {\n\tconst newProduct: Product = {\n\t\t...product,\n\t\tid: Date.now().toString(),\n\t\tcreated_at: new Date().toISOString(),\n\t};\n\tproducts.push(newProduct);\n\treturn newProduct;\n};\n\nexport const getProductById = (id: string): Product | undefined => {\n\treturn products.find((p) => p.id === id);\n};\n\n// Order functions\nexport const addOrder = (\n\torder: Omit<CustomerOrder, \"id\" | \"orderDate\">\n): CustomerOrder => {\n\tconst newOrder: CustomerOrder = {\n\t\t...order,\n\t\tid: Date.now().toString(),\n\t\torderDate: new Date().toISOString(),\n\t};\n\torders.push(newOrder);\n\n\t// Log order details to console for easy viewing\n\tconsole.log(\"🛒 NEW ORDER RECEIVED:\");\n\tconsole.log(\"Customer:\", newOrder.customerName);\n\tconsole.log(\"Phone:\", newOrder.phoneNo);\n\tconsole.log(\"Address:\", newOrder.address);\n\tconsole.log(\"Pin Code:\", newOrder.pinCode);\n\tconsole.log(\"Product:\", newOrder.productName);\n\tconsole.log(\"Price:\", `$${newOrder.productPrice}`);\n\tconsole.log(\"Quantity:\", newOrder.quantity);\n\tconsole.log(\"Total:\", `$${newOrder.totalPrice}`);\n\tconsole.log(\"Date:\", new Date(newOrder.orderDate).toLocaleString());\n\tconsole.log(\"-------------------\");\n\n\treturn newOrder;\n};\n\nexport const getOrders = (): CustomerOrder[] => {\n\treturn orders;\n};\n"], "names": [], "mappings": "AAAA,wCAAwC;;;;;;;;;;;;;AAwBxC,uFAAuF;AACvF,IAAI,WAAsB;IACzB;QACC,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,WACC;QACD,YAAY,IAAI,OAAO,WAAW;IACnC;CACA;AAED,IAAI,SAA0B,EAAE;AAGzB,MAAM,cAAc;IAC1B,OAAO;AACR;AAEO,MAAM,aAAa,CACzB;IAEA,MAAM,aAAsB;QAC3B,GAAG,OAAO;QACV,IAAI,KAAK,GAAG,GAAG,QAAQ;QACvB,YAAY,IAAI,OAAO,WAAW;IACnC;IACA,SAAS,IAAI,CAAC;IACd,OAAO;AACR;AAEO,MAAM,iBAAiB,CAAC;IAC9B,OAAO,SAAS,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;AACtC;AAGO,MAAM,WAAW,CACvB;IAEA,MAAM,WAA0B;QAC/B,GAAG,KAAK;QACR,IAAI,KAAK,GAAG,GAAG,QAAQ;QACvB,WAAW,IAAI,OAAO,WAAW;IAClC;IACA,OAAO,IAAI,CAAC;IAEZ,gDAAgD;IAChD,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,aAAa,SAAS,YAAY;IAC9C,QAAQ,GAAG,CAAC,UAAU,SAAS,OAAO;IACtC,QAAQ,GAAG,CAAC,YAAY,SAAS,OAAO;IACxC,QAAQ,GAAG,CAAC,aAAa,SAAS,OAAO;IACzC,QAAQ,GAAG,CAAC,YAAY,SAAS,WAAW;IAC5C,QAAQ,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,SAAS,YAAY,EAAE;IACjD,QAAQ,GAAG,CAAC,aAAa,SAAS,QAAQ;IAC1C,QAAQ,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;IAC/C,QAAQ,GAAG,CAAC,SAAS,IAAI,KAAK,SAAS,SAAS,EAAE,cAAc;IAChE,QAAQ,GAAG,CAAC;IAEZ,OAAO;AACR;AAEO,MAAM,YAAY;IACxB,OAAO;AACR", "debugId": null}}, {"offset": {"line": 72, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/shreeji/marketplace/src/app/add-product/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { addProduct } from \"@/lib/supabase\";\nimport { useRouter } from \"next/navigation\";\n\nexport default function AddProductPage() {\n\tconst [loading, setLoading] = useState(false);\n\tconst [name, setName] = useState(\"\");\n\tconst [description, setDescription] = useState(\"\");\n\tconst [price, setPrice] = useState(\"\");\n\tconst [imageUrl, setImageUrl] = useState(\"\");\n\tconst [message, setMessage] = useState(\"\");\n\tconst router = useRouter();\n\n\tconst handleSubmit = async (e: React.FormEvent) => {\n\t\te.preventDefault();\n\n\t\tsetLoading(true);\n\t\tsetMessage(\"\");\n\n\t\ttry {\n\t\t\taddProduct({\n\t\t\t\tname,\n\t\t\t\tdescription,\n\t\t\t\tprice: parseFloat(price),\n\t\t\t\timage_url: imageUrl || undefined,\n\t\t\t});\n\n\t\t\tsetMessage(\"Product added successfully!\");\n\t\t\tsetName(\"\");\n\t\t\tsetDescription(\"\");\n\t\t\tsetPrice(\"\");\n\t\t\tsetImageUrl(\"\");\n\n\t\t\t// Redirect to products page after 2 seconds\n\t\t\tsetTimeout(() => {\n\t\t\t\trouter.push(\"/products\");\n\t\t\t}, 2000);\n\t\t} catch (error: any) {\n\t\t\tsetMessage(`Error: ${error.message}`);\n\t\t} finally {\n\t\t\tsetLoading(false);\n\t\t}\n\t};\n\n\treturn (\n\t\t<div className=\"max-w-2xl mx-auto\">\n\t\t\t<div className=\"text-center mb-8\">\n\t\t\t\t<h1 className=\"text-4xl font-bold text-orange-800 mb-4 sacred-text\">\n\t\t\t\t\t⚙️ Admin Panel ⚙️\n\t\t\t\t</h1>\n\t\t\t\t<p className=\"text-lg text-orange-600\">Add New Sacred Item</p>\n\t\t\t</div>\n\n\t\t\t<form\n\t\t\t\tonSubmit={handleSubmit}\n\t\t\t\tclassName=\"bg-gradient-to-br from-white to-orange-50 p-8 rounded-2xl shadow-xl border-2 border-orange-200 divine-glow space-y-6\"\n\t\t\t>\n\t\t\t\t<div>\n\t\t\t\t\t<label\n\t\t\t\t\t\thtmlFor=\"name\"\n\t\t\t\t\t\tclassName=\"block text-sm font-bold text-orange-800 mb-2\"\n\t\t\t\t\t>\n\t\t\t\t\t\tSacred Item Name *\n\t\t\t\t\t</label>\n\t\t\t\t\t<input\n\t\t\t\t\t\tid=\"name\"\n\t\t\t\t\t\ttype=\"text\"\n\t\t\t\t\t\tvalue={name}\n\t\t\t\t\t\tonChange={(e) => setName(e.target.value)}\n\t\t\t\t\t\trequired\n\t\t\t\t\t\tclassName=\"w-full px-4 py-3 border-2 border-orange-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 text-orange-900 bg-white shadow-md font-medium\"\n\t\t\t\t\t\tplaceholder=\"Enter sacred item name\"\n\t\t\t\t\t/>\n\t\t\t\t</div>\n\n\t\t\t\t<div>\n\t\t\t\t\t<label\n\t\t\t\t\t\thtmlFor=\"description\"\n\t\t\t\t\t\tclassName=\"block text-sm font-bold text-orange-800 mb-2\"\n\t\t\t\t\t>\n\t\t\t\t\t\tSacred Description\n\t\t\t\t\t</label>\n\t\t\t\t\t<textarea\n\t\t\t\t\t\tid=\"description\"\n\t\t\t\t\t\tvalue={description}\n\t\t\t\t\t\tonChange={(e) => setDescription(e.target.value)}\n\t\t\t\t\t\trows={4}\n\t\t\t\t\t\tclassName=\"w-full px-4 py-3 border-2 border-orange-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 text-orange-900 bg-white shadow-md font-medium\"\n\t\t\t\t\t\tplaceholder=\"Describe the spiritual significance and details\"\n\t\t\t\t\t/>\n\t\t\t\t</div>\n\n\t\t\t\t<div>\n\t\t\t\t\t<label\n\t\t\t\t\t\thtmlFor=\"price\"\n\t\t\t\t\t\tclassName=\"block text-sm font-bold text-orange-800 mb-2\"\n\t\t\t\t\t>\n\t\t\t\t\t\tPrice (₹) *\n\t\t\t\t\t</label>\n\t\t\t\t\t<input\n\t\t\t\t\t\tid=\"price\"\n\t\t\t\t\t\ttype=\"number\"\n\t\t\t\t\t\tstep=\"0.01\"\n\t\t\t\t\t\tmin=\"0\"\n\t\t\t\t\t\tvalue={price}\n\t\t\t\t\t\tonChange={(e) => setPrice(e.target.value)}\n\t\t\t\t\t\trequired\n\t\t\t\t\t\tclassName=\"w-full px-4 py-3 border-2 border-orange-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 text-orange-900 bg-white shadow-md font-medium\"\n\t\t\t\t\t\tplaceholder=\"0.00\"\n\t\t\t\t\t/>\n\t\t\t\t</div>\n\n\t\t\t\t<div>\n\t\t\t\t\t<label\n\t\t\t\t\t\thtmlFor=\"imageUrl\"\n\t\t\t\t\t\tclassName=\"block text-sm font-bold text-orange-800 mb-2\"\n\t\t\t\t\t>\n\t\t\t\t\t\tSacred Image URL (optional)\n\t\t\t\t\t</label>\n\t\t\t\t\t<input\n\t\t\t\t\t\tid=\"imageUrl\"\n\t\t\t\t\t\ttype=\"url\"\n\t\t\t\t\t\tvalue={imageUrl}\n\t\t\t\t\t\tonChange={(e) => setImageUrl(e.target.value)}\n\t\t\t\t\t\tclassName=\"w-full px-4 py-3 border-2 border-orange-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 text-orange-900 bg-white shadow-md font-medium\"\n\t\t\t\t\t\tplaceholder=\"https://example.com/sacred-image.jpg\"\n\t\t\t\t\t/>\n\t\t\t\t\t<p className=\"text-sm text-orange-600 mt-2\">\n\t\t\t\t\t\t📸 Add a beautiful image of your sacred item\n\t\t\t\t\t</p>\n\t\t\t\t</div>\n\n\t\t\t\t<button\n\t\t\t\t\ttype=\"submit\"\n\t\t\t\t\tdisabled={loading}\n\t\t\t\t\tclassName=\"w-full spiritual-gradient text-white py-4 px-6 rounded-full hover:scale-105 transform transition-all duration-300 font-bold text-lg shadow-lg divine-glow disabled:opacity-50\"\n\t\t\t\t>\n\t\t\t\t\t{loading ? \"🙏 Adding Sacred Item...\" : \"✨ Add Sacred Item ✨\"}\n\t\t\t\t</button>\n\n\t\t\t\t{message && (\n\t\t\t\t\t<div\n\t\t\t\t\t\tclassName={`p-3 rounded-md text-sm ${\n\t\t\t\t\t\t\tmessage.includes(\"Error\")\n\t\t\t\t\t\t\t\t? \"bg-red-100 text-red-700\"\n\t\t\t\t\t\t\t\t: \"bg-green-100 text-green-700\"\n\t\t\t\t\t\t}`}\n\t\t\t\t\t>\n\t\t\t\t\t\t{message}\n\t\t\t\t\t</div>\n\t\t\t\t)}\n\t\t\t</form>\n\t\t</div>\n\t);\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IACvC,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,iNAAQ,EAAC;IACjC,MAAM,CAAC,aAAa,eAAe,GAAG,IAAA,iNAAQ,EAAC;IAC/C,MAAM,CAAC,OAAO,SAAS,GAAG,IAAA,iNAAQ,EAAC;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iNAAQ,EAAC;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IACvC,MAAM,SAAS,IAAA,+IAAS;IAExB,MAAM,eAAe,OAAO;QAC3B,EAAE,cAAc;QAEhB,WAAW;QACX,WAAW;QAEX,IAAI;YACH,IAAA,oIAAU,EAAC;gBACV;gBACA;gBACA,OAAO,WAAW;gBAClB,WAAW,YAAY;YACxB;YAEA,WAAW;YACX,QAAQ;YACR,eAAe;YACf,SAAS;YACT,YAAY;YAEZ,4CAA4C;YAC5C,WAAW;gBACV,OAAO,IAAI,CAAC;YACb,GAAG;QACJ,EAAE,OAAO,OAAY;YACpB,WAAW,CAAC,OAAO,EAAE,MAAM,OAAO,EAAE;QACrC,SAAU;YACT,WAAW;QACZ;IACD;IAEA,qBACC,8OAAC;QAAI,WAAU;;0BACd,8OAAC;gBAAI,WAAU;;kCACd,8OAAC;wBAAG,WAAU;kCAAsD;;;;;;kCAGpE,8OAAC;wBAAE,WAAU;kCAA0B;;;;;;;;;;;;0BAGxC,8OAAC;gBACA,UAAU;gBACV,WAAU;;kCAEV,8OAAC;;0CACA,8OAAC;gCACA,SAAQ;gCACR,WAAU;0CACV;;;;;;0CAGD,8OAAC;gCACA,IAAG;gCACH,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;gCACvC,QAAQ;gCACR,WAAU;gCACV,aAAY;;;;;;;;;;;;kCAId,8OAAC;;0CACA,8OAAC;gCACA,SAAQ;gCACR,WAAU;0CACV;;;;;;0CAGD,8OAAC;gCACA,IAAG;gCACH,OAAO;gCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;gCAC9C,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;;;;;;;kCAId,8OAAC;;0CACA,8OAAC;gCACA,SAAQ;gCACR,WAAU;0CACV;;;;;;0CAGD,8OAAC;gCACA,IAAG;gCACH,MAAK;gCACL,MAAK;gCACL,KAAI;gCACJ,OAAO;gCACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;gCACxC,QAAQ;gCACR,WAAU;gCACV,aAAY;;;;;;;;;;;;kCAId,8OAAC;;0CACA,8OAAC;gCACA,SAAQ;gCACR,WAAU;0CACV;;;;;;0CAGD,8OAAC;gCACA,IAAG;gCACH,MAAK;gCACL,OAAO;gCACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;gCAC3C,WAAU;gCACV,aAAY;;;;;;0CAEb,8OAAC;gCAAE,WAAU;0CAA+B;;;;;;;;;;;;kCAK7C,8OAAC;wBACA,MAAK;wBACL,UAAU;wBACV,WAAU;kCAET,UAAU,6BAA6B;;;;;;oBAGxC,yBACA,8OAAC;wBACA,WAAW,CAAC,uBAAuB,EAClC,QAAQ,QAAQ,CAAC,WACd,4BACA,+BACF;kCAED;;;;;;;;;;;;;;;;;;AAMP", "debugId": null}}, {"offset": {"line": 313, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/shreeji/marketplace/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}