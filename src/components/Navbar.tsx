"use client";

import Link from "next/link";

export default function Navbar() {
	return (
		<nav className="spiritual-gradient shadow-lg border-b-2 border-orange-300 divine-glow">
			<div className="container mx-auto px-4">
				<div className="flex justify-between items-center h-16">
					<Link href="/" className="text-2xl font-bold text-white sacred-text">
						Shri Hit Store
					</Link>

					<div className="flex items-center space-x-6">
						<Link
							href="/products"
							className="text-white hover:text-yellow-200 font-semibold transition-colors duration-200"
						>
							🛍️ Shop
						</Link>
						<Link
							href="/how-to-order"
							className="text-white hover:text-yellow-200 font-semibold transition-colors duration-200"
						>
							❓ How to Order
						</Link>
						<Link
							href="/orders"
							className="text-white hover:text-yellow-200 font-semibold transition-colors duration-200"
						>
							📋 Orders
						</Link>
						<Link
							href="/add-product"
							className="text-orange-100 hover:text-yellow-200 text-sm font-medium transition-colors duration-200"
						>
							⚙️ Admin
						</Link>
					</div>
				</div>
			</div>
		</nav>
	);
}
