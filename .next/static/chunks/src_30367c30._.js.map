{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/shreeji/marketplace/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Database Types\nexport interface User {\n  id: string\n  email: string\n  created_at: string\n}\n\nexport interface Product {\n  id: string\n  name: string\n  description: string\n  price: number\n  image_url?: string\n  user_id: string\n  created_at: string\n}\n\nexport interface Order {\n  id: string\n  user_id: string\n  product_id: string\n  quantity: number\n  total_price: number\n  status: 'pending' | 'completed' | 'cancelled'\n  created_at: string\n}\n"], "names": [], "mappings": ";;;;AAEoB;AAFpB;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,IAAA,0MAAY,EAAC,aAAa", "debugId": null}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/shreeji/marketplace/src/components/Navbar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { supabase } from '@/lib/supabase'\nimport { User } from '@supabase/supabase-js'\nimport Link from 'next/link'\n\nexport default function Navbar() {\n  const [user, setUser] = useState<User | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    // Get initial user\n    const getUser = async () => {\n      const { data: { user } } = await supabase.auth.getUser()\n      setUser(user)\n      setLoading(false)\n    }\n    \n    getUser()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      (event, session) => {\n        setUser(session?.user ?? null)\n        setLoading(false)\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [])\n\n  const handleSignOut = async () => {\n    await supabase.auth.signOut()\n  }\n\n  return (\n    <nav className=\"bg-white shadow-sm border-b\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex justify-between items-center h-16\">\n          <Link href=\"/\" className=\"text-xl font-bold text-gray-900\">\n            Simple Marketplace\n          </Link>\n          \n          <div className=\"flex items-center space-x-4\">\n            {loading ? (\n              <div className=\"text-gray-500\">Loading...</div>\n            ) : user ? (\n              <>\n                <Link \n                  href=\"/products\" \n                  className=\"text-gray-700 hover:text-gray-900\"\n                >\n                  Browse Products\n                </Link>\n                <Link \n                  href=\"/add-product\" \n                  className=\"text-gray-700 hover:text-gray-900\"\n                >\n                  Sell Product\n                </Link>\n                <Link \n                  href=\"/orders\" \n                  className=\"text-gray-700 hover:text-gray-900\"\n                >\n                  My Orders\n                </Link>\n                <span className=\"text-gray-600 text-sm\">\n                  {user.email}\n                </span>\n                <button\n                  onClick={handleSignOut}\n                  className=\"bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700\"\n                >\n                  Sign Out\n                </button>\n              </>\n            ) : (\n              <Link \n                href=\"/auth\" \n                className=\"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700\"\n              >\n                Sign In\n              </Link>\n            )}\n          </div>\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;;;AALA;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,yKAAQ,EAAc;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IAEvC,IAAA,0KAAS;4BAAC;YACR,mBAAmB;YACnB,MAAM;4CAAU;oBACd,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,qIAAQ,CAAC,IAAI,CAAC,OAAO;oBACtD,QAAQ;oBACR,WAAW;gBACb;;YAEA;YAEA,0BAA0B;YAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,qIAAQ,CAAC,IAAI,CAAC,iBAAiB;oCAChE,CAAC,OAAO;wBACE;oBAAR,QAAQ,CAAA,gBAAA,oBAAA,8BAAA,QAAS,IAAI,cAAb,2BAAA,gBAAiB;oBACzB,WAAW;gBACb;;YAGF;oCAAO,IAAM,aAAa,WAAW;;QACvC;2BAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,MAAM,qIAAQ,CAAC,IAAI,CAAC,OAAO;IAC7B;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,0KAAI;wBAAC,MAAK;wBAAI,WAAU;kCAAkC;;;;;;kCAI3D,6LAAC;wBAAI,WAAU;kCACZ,wBACC,6LAAC;4BAAI,WAAU;sCAAgB;;;;;mCAC7B,qBACF;;8CACE,6LAAC,0KAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,0KAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC,0KAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCAAK,WAAU;8CACb,KAAK,KAAK;;;;;;8CAEb,6LAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;yDAKH,6LAAC,0KAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAnFwB;KAAA", "debugId": null}}]}