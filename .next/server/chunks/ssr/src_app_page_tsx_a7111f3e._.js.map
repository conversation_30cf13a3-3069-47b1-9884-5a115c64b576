{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/shreeji/marketplace/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { getProducts, Product } from \"@/lib/supabase\";\nimport Link from \"next/link\";\n\nexport default function Home() {\n\tconst [products, setProducts] = useState<Product[]>([]);\n\tconst [loading, setLoading] = useState(true);\n\tconst [searchTerm, setSearchTerm] = useState(\"\");\n\n\tuseEffect(() => {\n\t\tfetchProducts();\n\t}, []);\n\n\tconst fetchProducts = () => {\n\t\ttry {\n\t\t\tconst allProducts = getProducts();\n\t\t\tsetProducts(allProducts);\n\t\t} catch (error) {\n\t\t\tconsole.error(\"Error fetching products:\", error);\n\t\t} finally {\n\t\t\tsetLoading(false);\n\t\t}\n\t};\n\n\tconst filteredProducts = products.filter(\n\t\t(product) =>\n\t\t\tproduct.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n\t\t\tproduct.description?.toLowerCase().includes(searchTerm.toLowerCase())\n\t);\n\n\treturn (\n\t\t<div className=\"space-y-8\">\n\t\t\t{/* Hero Section */}\n\t\t\t<div className=\"text-center py-12 bg-white rounded-lg shadow-sm\">\n\t\t\t\t<h1 className=\"text-4xl font-bold text-gray-900 mb-4\">\n\t\t\t\t\tWelcome to Simple Marketplace\n\t\t\t\t</h1>\n\t\t\t\t<p className=\"text-xl text-gray-600 mb-8\">\n\t\t\t\t\tBuy and sell products easily in our simple, clean marketplace\n\t\t\t\t</p>\n\t\t\t\t<div className=\"flex justify-center space-x-4\">\n\t\t\t\t\t<Link\n\t\t\t\t\t\thref=\"/products\"\n\t\t\t\t\t\tclassName=\"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700\"\n\t\t\t\t\t>\n\t\t\t\t\t\tBrowse All Products\n\t\t\t\t\t</Link>\n\t\t\t\t\t<Link\n\t\t\t\t\t\thref=\"/add-product\"\n\t\t\t\t\t\tclassName=\"bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700\"\n\t\t\t\t\t>\n\t\t\t\t\t\tStart Selling\n\t\t\t\t\t</Link>\n\t\t\t\t</div>\n\t\t\t</div>\n\n\t\t\t{/* Search */}\n\t\t\t<div className=\"max-w-md mx-auto\">\n\t\t\t\t<input\n\t\t\t\t\ttype=\"text\"\n\t\t\t\t\tplaceholder=\"Search products...\"\n\t\t\t\t\tvalue={searchTerm}\n\t\t\t\t\tonChange={(e) => setSearchTerm(e.target.value)}\n\t\t\t\t\tclassName=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n\t\t\t\t/>\n\t\t\t</div>\n\n\t\t\t{/* Recent Products */}\n\t\t\t<div>\n\t\t\t\t<h2 className=\"text-2xl font-bold text-gray-900 mb-6\">\n\t\t\t\t\tRecent Products\n\t\t\t\t</h2>\n\n\t\t\t\t{loading ? (\n\t\t\t\t\t<div className=\"text-center py-8\">\n\t\t\t\t\t\t<div className=\"text-gray-500\">Loading products...</div>\n\t\t\t\t\t</div>\n\t\t\t\t) : filteredProducts.length === 0 ? (\n\t\t\t\t\t<div className=\"text-center py-8\">\n\t\t\t\t\t\t<div className=\"text-gray-500\">\n\t\t\t\t\t\t\t{searchTerm\n\t\t\t\t\t\t\t\t? \"No products found matching your search.\"\n\t\t\t\t\t\t\t\t: \"No products available yet.\"}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<Link\n\t\t\t\t\t\t\thref=\"/add-product\"\n\t\t\t\t\t\t\tclassName=\"text-blue-600 hover:text-blue-500 mt-2 inline-block\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\tBe the first to add a product!\n\t\t\t\t\t\t</Link>\n\t\t\t\t\t</div>\n\t\t\t\t) : (\n\t\t\t\t\t<div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n\t\t\t\t\t\t{filteredProducts.map((product) => (\n\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\tkey={product.id}\n\t\t\t\t\t\t\t\tclassName=\"bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{product.image_url && (\n\t\t\t\t\t\t\t\t\t<img\n\t\t\t\t\t\t\t\t\t\tsrc={product.image_url}\n\t\t\t\t\t\t\t\t\t\talt={product.name}\n\t\t\t\t\t\t\t\t\t\tclassName=\"w-full h-48 object-cover rounded-t-lg\"\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t<div className=\"p-4\">\n\t\t\t\t\t\t\t\t\t<h3 className=\"font-semibold text-gray-900 mb-2\">\n\t\t\t\t\t\t\t\t\t\t{product.name}\n\t\t\t\t\t\t\t\t\t</h3>\n\t\t\t\t\t\t\t\t\t<p className=\"text-gray-600 text-sm mb-3 line-clamp-2\">\n\t\t\t\t\t\t\t\t\t\t{product.description}\n\t\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t\t\t<div className=\"flex justify-between items-center\">\n\t\t\t\t\t\t\t\t\t\t<span className=\"text-lg font-bold text-green-600\">\n\t\t\t\t\t\t\t\t\t\t\t${product.price}\n\t\t\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t\t\t\t<Link\n\t\t\t\t\t\t\t\t\t\t\thref={`/products/${product.id}`}\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700\"\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\tView Details\n\t\t\t\t\t\t\t\t\t\t</Link>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t))}\n\t\t\t\t\t</div>\n\t\t\t\t)}\n\t\t\t</div>\n\t\t</div>\n\t);\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAMe,SAAS;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,iNAAQ,EAAY,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,iNAAQ,EAAC;IAE7C,IAAA,kNAAS,EAAC;QACT;IACD,GAAG,EAAE;IAEL,MAAM,gBAAgB;QACrB,IAAI;YACH,MAAM,cAAc,IAAA,qIAAW;YAC/B,YAAY;QACb,EAAE,OAAO,OAAO;YACf,QAAQ,KAAK,CAAC,4BAA4B;QAC3C,SAAU;YACT,WAAW;QACZ;IACD;IAEA,MAAM,mBAAmB,SAAS,MAAM,CACvC,CAAC,UACA,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC1D,QAAQ,WAAW,EAAE,cAAc,SAAS,WAAW,WAAW;IAGpE,qBACC,8OAAC;QAAI,WAAU;;0BAEd,8OAAC;gBAAI,WAAU;;kCACd,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAG1C,8OAAC;wBAAI,WAAU;;0CACd,8OAAC,uKAAI;gCACJ,MAAK;gCACL,WAAU;0CACV;;;;;;0CAGD,8OAAC,uKAAI;gCACJ,MAAK;gCACL,WAAU;0CACV;;;;;;;;;;;;;;;;;;0BAOH,8OAAC;gBAAI,WAAU;0BACd,cAAA,8OAAC;oBACA,MAAK;oBACL,aAAY;oBACZ,OAAO;oBACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oBAC7C,WAAU;;;;;;;;;;;0BAKZ,8OAAC;;kCACA,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;oBAIrD,wBACA,8OAAC;wBAAI,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCAAgB;;;;;;;;;;+BAE7B,iBAAiB,MAAM,KAAK,kBAC/B,8OAAC;wBAAI,WAAU;;0CACd,8OAAC;gCAAI,WAAU;0CACb,aACE,4CACA;;;;;;0CAEJ,8OAAC,uKAAI;gCACJ,MAAK;gCACL,WAAU;0CACV;;;;;;;;;;;6CAKF,8OAAC;wBAAI,WAAU;kCACb,iBAAiB,GAAG,CAAC,CAAC,wBACtB,8OAAC;gCAEA,WAAU;;oCAET,QAAQ,SAAS,kBACjB,8OAAC;wCACA,KAAK,QAAQ,SAAS;wCACtB,KAAK,QAAQ,IAAI;wCACjB,WAAU;;;;;;kDAGZ,8OAAC;wCAAI,WAAU;;0DACd,8OAAC;gDAAG,WAAU;0DACZ,QAAQ,IAAI;;;;;;0DAEd,8OAAC;gDAAE,WAAU;0DACX,QAAQ,WAAW;;;;;;0DAErB,8OAAC;gDAAI,WAAU;;kEACd,8OAAC;wDAAK,WAAU;;4DAAmC;4DAChD,QAAQ,KAAK;;;;;;;kEAEhB,8OAAC,uKAAI;wDACJ,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;wDAC/B,WAAU;kEACV;;;;;;;;;;;;;;;;;;;+BAxBE,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;AAoCvB", "debugId": null}}]}