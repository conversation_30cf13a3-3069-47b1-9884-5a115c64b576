{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/shreeji/marketplace/src/lib/supabase.ts"], "sourcesContent": ["// Simple data types for our marketplace\nexport interface Product {\n\tid: string;\n\tname: string;\n\tdescription: string;\n\tprice: number;\n\timage_url?: string;\n\tcreated_at: string;\n}\n\nexport interface CustomerOrder {\n\tid: string;\n\tcustomerName: string;\n\taddress: string;\n\tphoneNo: string;\n\tpinCode: string;\n\tproductName: string;\n\tproductPrice: number;\n\tquantity: number;\n\ttotalPrice: number;\n\torderDate: string;\n\tstatus: \"pending\" | \"completed\" | \"cancelled\";\n}\n\n// Simple in-memory storage (you can replace this with a file or simple database later)\nlet products: Product[] = [\n\t{\n\t\tid: \"1\",\n\t\tname: \"Sample Product\",\n\t\tdescription: \"This is a sample product to get you started\",\n\t\tprice: 29.99,\n\t\timage_url:\n\t\t\t\"https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400\",\n\t\tcreated_at: new Date().toISOString(),\n\t},\n];\n\nlet orders: CustomerOrder[] = [];\n\n// Product functions\nexport const getProducts = (): Product[] => {\n\treturn products;\n};\n\nexport const addProduct = (\n\tproduct: Omit<Product, \"id\" | \"created_at\">\n): Product => {\n\tconst newProduct: Product = {\n\t\t...product,\n\t\tid: Date.now().toString(),\n\t\tcreated_at: new Date().toISOString(),\n\t};\n\tproducts.push(newProduct);\n\treturn newProduct;\n};\n\nexport const getProductById = (id: string): Product | undefined => {\n\treturn products.find((p) => p.id === id);\n};\n\n// Order functions\nexport const addOrder = (\n\torder: Omit<CustomerOrder, \"id\" | \"orderDate\">\n): CustomerOrder => {\n\tconst newOrder: CustomerOrder = {\n\t\t...order,\n\t\tid: Date.now().toString(),\n\t\torderDate: new Date().toISOString(),\n\t};\n\torders.push(newOrder);\n\n\t// Log order details to console for easy viewing\n\tconsole.log(\"🛒 NEW ORDER RECEIVED:\");\n\tconsole.log(\"Customer:\", newOrder.customerName);\n\tconsole.log(\"Phone:\", newOrder.phoneNo);\n\tconsole.log(\"Address:\", newOrder.address);\n\tconsole.log(\"Pin Code:\", newOrder.pinCode);\n\tconsole.log(\"Product:\", newOrder.productName);\n\tconsole.log(\"Price:\", `$${newOrder.productPrice}`);\n\tconsole.log(\"Quantity:\", newOrder.quantity);\n\tconsole.log(\"Total:\", `$${newOrder.totalPrice}`);\n\tconsole.log(\"Date:\", new Date(newOrder.orderDate).toLocaleString());\n\tconsole.log(\"-------------------\");\n\n\treturn newOrder;\n};\n\nexport const getOrders = (): CustomerOrder[] => {\n\treturn orders;\n};\n"], "names": [], "mappings": "AAAA,wCAAwC;;;;;;;;;;;;;AAwBxC,uFAAuF;AACvF,IAAI,WAAsB;IACzB;QACC,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,WACC;QACD,YAAY,IAAI,OAAO,WAAW;IACnC;CACA;AAED,IAAI,SAA0B,EAAE;AAGzB,MAAM,cAAc;IAC1B,OAAO;AACR;AAEO,MAAM,aAAa,CACzB;IAEA,MAAM,aAAsB;QAC3B,GAAG,OAAO;QACV,IAAI,KAAK,GAAG,GAAG,QAAQ;QACvB,YAAY,IAAI,OAAO,WAAW;IACnC;IACA,SAAS,IAAI,CAAC;IACd,OAAO;AACR;AAEO,MAAM,iBAAiB,CAAC;IAC9B,OAAO,SAAS,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;AACtC;AAGO,MAAM,WAAW,CACvB;IAEA,MAAM,WAA0B;QAC/B,GAAG,KAAK;QACR,IAAI,KAAK,GAAG,GAAG,QAAQ;QACvB,WAAW,IAAI,OAAO,WAAW;IAClC;IACA,OAAO,IAAI,CAAC;IAEZ,gDAAgD;IAChD,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,aAAa,SAAS,YAAY;IAC9C,QAAQ,GAAG,CAAC,UAAU,SAAS,OAAO;IACtC,QAAQ,GAAG,CAAC,YAAY,SAAS,OAAO;IACxC,QAAQ,GAAG,CAAC,aAAa,SAAS,OAAO;IACzC,QAAQ,GAAG,CAAC,YAAY,SAAS,WAAW;IAC5C,QAAQ,GAAG,CAAC,UAAU,AAAC,IAAyB,OAAtB,SAAS,YAAY;IAC/C,QAAQ,GAAG,CAAC,aAAa,SAAS,QAAQ;IAC1C,QAAQ,GAAG,CAAC,UAAU,AAAC,IAAuB,OAApB,SAAS,UAAU;IAC7C,QAAQ,GAAG,CAAC,SAAS,IAAI,KAAK,SAAS,SAAS,EAAE,cAAc;IAChE,QAAQ,GAAG,CAAC;IAEZ,OAAO;AACR;AAEO,MAAM,YAAY;IACxB,OAAO;AACR", "debugId": null}}, {"offset": {"line": 75, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/shreeji/marketplace/src/app/orders/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { getOrders, CustomerOrder } from \"@/lib/supabase\";\n\nexport default function OrdersPage() {\n\tconst [orders, setOrders] = useState<CustomerOrder[]>([]);\n\tconst [loading, setLoading] = useState(true);\n\n\tuseEffect(() => {\n\t\tfetchOrders();\n\t}, []);\n\n\tconst fetchOrders = () => {\n\t\ttry {\n\t\t\tconst allOrders = getOrders();\n\t\t\tsetOrders(allOrders);\n\t\t} catch (error) {\n\t\t\tconsole.error(\"Error fetching orders:\", error);\n\t\t} finally {\n\t\t\tsetLoading(false);\n\t\t}\n\t};\n\n\tconst getStatusColor = (status: string) => {\n\t\tswitch (status) {\n\t\t\tcase \"completed\":\n\t\t\t\treturn \"bg-green-100 text-green-800 border border-green-300\";\n\t\t\tcase \"pending\":\n\t\t\t\treturn \"bg-orange-100 text-orange-800 border border-orange-300\";\n\t\t\tcase \"cancelled\":\n\t\t\t\treturn \"bg-red-100 text-red-800 border border-red-300\";\n\t\t\tdefault:\n\t\t\t\treturn \"bg-orange-50 text-orange-700 border border-orange-200\";\n\t\t}\n\t};\n\n\treturn (\n\t\t<div className=\"space-y-8\">\n\t\t\t<div className=\"text-center\">\n\t\t\t\t<h1 className=\"text-4xl font-bold text-orange-800 sacred-text\">\n\t\t\t\t\t📋 Customer Orders 📋\n\t\t\t\t</h1>\n\t\t\t\t<p className=\"text-lg text-orange-600 mt-2\">\n\t\t\t\t\tSacred item orders from devotees\n\t\t\t\t</p>\n\t\t\t</div>\n\n\t\t\t{loading ? (\n\t\t\t\t<div className=\"text-center py-12\">\n\t\t\t\t\t<div className=\"text-orange-600 text-lg\">🙏 Loading orders...</div>\n\t\t\t\t</div>\n\t\t\t) : orders.length === 0 ? (\n\t\t\t\t<div className=\"text-center py-12\">\n\t\t\t\t\t<div className=\"text-orange-600 text-lg mb-4\">\n\t\t\t\t\t\t📿 No orders received yet.\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t) : (\n\t\t\t\t<div className=\"space-y-4\">\n\t\t\t\t\t{orders.map((order) => (\n\t\t\t\t\t\t<div\n\t\t\t\t\t\t\tkey={order.id}\n\t\t\t\t\t\t\tclassName=\"bg-gradient-to-br from-white to-orange-50 rounded-2xl shadow-xl border-2 border-orange-200 p-8 divine-glow\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t<div className=\"grid md:grid-cols-2 gap-8\">\n\t\t\t\t\t\t\t\t{/* Customer Details */}\n\t\t\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t\t\t<h3 className=\"text-xl font-bold text-orange-800 mb-4 sacred-text\">\n\t\t\t\t\t\t\t\t\t\t👤 Devotee Details\n\t\t\t\t\t\t\t\t\t</h3>\n\t\t\t\t\t\t\t\t\t<div className=\"space-y-3 text-sm\">\n\t\t\t\t\t\t\t\t\t\t<p className=\"text-orange-700\">\n\t\t\t\t\t\t\t\t\t\t\t<span className=\"font-bold text-orange-800\">Name:</span>{\" \"}\n\t\t\t\t\t\t\t\t\t\t\t{order.customerName}\n\t\t\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t\t\t\t<p className=\"text-orange-700\">\n\t\t\t\t\t\t\t\t\t\t\t<span className=\"font-bold text-orange-800\">Phone:</span>{\" \"}\n\t\t\t\t\t\t\t\t\t\t\t{order.phoneNo}\n\t\t\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t\t\t\t<p className=\"text-orange-700\">\n\t\t\t\t\t\t\t\t\t\t\t<span className=\"font-bold text-orange-800\">\n\t\t\t\t\t\t\t\t\t\t\t\tAddress:\n\t\t\t\t\t\t\t\t\t\t\t</span>{\" \"}\n\t\t\t\t\t\t\t\t\t\t\t{order.address}\n\t\t\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t\t\t\t<p className=\"text-orange-700\">\n\t\t\t\t\t\t\t\t\t\t\t<span className=\"font-bold text-orange-800\">\n\t\t\t\t\t\t\t\t\t\t\t\tPin Code:\n\t\t\t\t\t\t\t\t\t\t\t</span>{\" \"}\n\t\t\t\t\t\t\t\t\t\t\t{order.pinCode}\n\t\t\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t\t{/* Order Details */}\n\t\t\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t\t\t<h3 className=\"text-xl font-bold text-orange-800 mb-4 sacred-text\">\n\t\t\t\t\t\t\t\t\t\t📿 Sacred Order Details\n\t\t\t\t\t\t\t\t\t</h3>\n\t\t\t\t\t\t\t\t\t<div className=\"space-y-3 text-sm\">\n\t\t\t\t\t\t\t\t\t\t<p className=\"text-orange-700\">\n\t\t\t\t\t\t\t\t\t\t\t<span className=\"font-bold text-orange-800\">\n\t\t\t\t\t\t\t\t\t\t\t\tSacred Item:\n\t\t\t\t\t\t\t\t\t\t\t</span>{\" \"}\n\t\t\t\t\t\t\t\t\t\t\t{order.productName}\n\t\t\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t\t\t\t<p className=\"text-orange-700\">\n\t\t\t\t\t\t\t\t\t\t\t<span className=\"font-bold text-orange-800\">Price:</span>{\" \"}\n\t\t\t\t\t\t\t\t\t\t\t₹{order.productPrice}\n\t\t\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t\t\t\t<p className=\"text-orange-700\">\n\t\t\t\t\t\t\t\t\t\t\t<span className=\"font-bold text-orange-800\">\n\t\t\t\t\t\t\t\t\t\t\t\tQuantity:\n\t\t\t\t\t\t\t\t\t\t\t</span>{\" \"}\n\t\t\t\t\t\t\t\t\t\t\t{order.quantity}\n\t\t\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t\t\t\t<p className=\"text-orange-700\">\n\t\t\t\t\t\t\t\t\t\t\t<span className=\"font-bold text-orange-800\">Total:</span>{\" \"}\n\t\t\t\t\t\t\t\t\t\t\t<span className=\"text-xl font-bold text-orange-600\">\n\t\t\t\t\t\t\t\t\t\t\t\t₹{order.totalPrice}\n\t\t\t\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t\t\t\t<p className=\"text-orange-700\">\n\t\t\t\t\t\t\t\t\t\t\t<span className=\"font-bold text-orange-800\">\n\t\t\t\t\t\t\t\t\t\t\t\tOrder Date:\n\t\t\t\t\t\t\t\t\t\t\t</span>{\" \"}\n\t\t\t\t\t\t\t\t\t\t\t{new Date(order.orderDate).toLocaleDateString()}\n\t\t\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t<div className=\"mt-6 pt-4 border-t-2 border-orange-200 flex justify-between items-center\">\n\t\t\t\t\t\t\t\t<span\n\t\t\t\t\t\t\t\t\tclassName={`px-4 py-2 rounded-full text-sm font-bold ${getStatusColor(\n\t\t\t\t\t\t\t\t\t\torder.status\n\t\t\t\t\t\t\t\t\t)}`}\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t{order.status.charAt(0).toUpperCase() + order.status.slice(1)}\n\t\t\t\t\t\t\t\t</span>\n\n\t\t\t\t\t\t\t\t<div className=\"text-sm font-semibold text-orange-600\">\n\t\t\t\t\t\t\t\t\tOrder ID: SH{order.id.slice(-6)}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t))}\n\t\t\t\t</div>\n\t\t\t)}\n\t\t</div>\n\t);\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;;;AAHA;;;AAKe,SAAS;;IACvB,MAAM,CAAC,QAAQ,UAAU,GAAG,IAAA,yKAAQ,EAAkB,EAAE;IACxD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IAEvC,IAAA,0KAAS;gCAAC;YACT;QACD;+BAAG,EAAE;IAEL,MAAM,cAAc;QACnB,IAAI;YACH,MAAM,YAAY,IAAA,sIAAS;YAC3B,UAAU;QACX,EAAE,OAAO,OAAO;YACf,QAAQ,KAAK,CAAC,0BAA0B;QACzC,SAAU;YACT,WAAW;QACZ;IACD;IAEA,MAAM,iBAAiB,CAAC;QACvB,OAAQ;YACP,KAAK;gBACJ,OAAO;YACR,KAAK;gBACJ,OAAO;YACR,KAAK;gBACJ,OAAO;YACR;gBACC,OAAO;QACT;IACD;IAEA,qBACC,6LAAC;QAAI,WAAU;;0BACd,6LAAC;gBAAI,WAAU;;kCACd,6LAAC;wBAAG,WAAU;kCAAiD;;;;;;kCAG/D,6LAAC;wBAAE,WAAU;kCAA+B;;;;;;;;;;;;YAK5C,wBACA,6LAAC;gBAAI,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;8BAA0B;;;;;;;;;;uBAEvC,OAAO,MAAM,KAAK,kBACrB,6LAAC;gBAAI,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;8BAA+B;;;;;;;;;;qCAK/C,6LAAC;gBAAI,WAAU;0BACb,OAAO,GAAG,CAAC,CAAC,sBACZ,6LAAC;wBAEA,WAAU;;0CAEV,6LAAC;gCAAI,WAAU;;kDAEd,6LAAC;;0DACA,6LAAC;gDAAG,WAAU;0DAAqD;;;;;;0DAGnE,6LAAC;gDAAI,WAAU;;kEACd,6LAAC;wDAAE,WAAU;;0EACZ,6LAAC;gEAAK,WAAU;0EAA4B;;;;;;4DAAa;4DACxD,MAAM,YAAY;;;;;;;kEAEpB,6LAAC;wDAAE,WAAU;;0EACZ,6LAAC;gEAAK,WAAU;0EAA4B;;;;;;4DAAc;4DACzD,MAAM,OAAO;;;;;;;kEAEf,6LAAC;wDAAE,WAAU;;0EACZ,6LAAC;gEAAK,WAAU;0EAA4B;;;;;;4DAEpC;4DACP,MAAM,OAAO;;;;;;;kEAEf,6LAAC;wDAAE,WAAU;;0EACZ,6LAAC;gEAAK,WAAU;0EAA4B;;;;;;4DAEpC;4DACP,MAAM,OAAO;;;;;;;;;;;;;;;;;;;kDAMjB,6LAAC;;0DACA,6LAAC;gDAAG,WAAU;0DAAqD;;;;;;0DAGnE,6LAAC;gDAAI,WAAU;;kEACd,6LAAC;wDAAE,WAAU;;0EACZ,6LAAC;gEAAK,WAAU;0EAA4B;;;;;;4DAEpC;4DACP,MAAM,WAAW;;;;;;;kEAEnB,6LAAC;wDAAE,WAAU;;0EACZ,6LAAC;gEAAK,WAAU;0EAA4B;;;;;;4DAAc;4DAAI;4DAC5D,MAAM,YAAY;;;;;;;kEAErB,6LAAC;wDAAE,WAAU;;0EACZ,6LAAC;gEAAK,WAAU;0EAA4B;;;;;;4DAEpC;4DACP,MAAM,QAAQ;;;;;;;kEAEhB,6LAAC;wDAAE,WAAU;;0EACZ,6LAAC;gEAAK,WAAU;0EAA4B;;;;;;4DAAc;0EAC1D,6LAAC;gEAAK,WAAU;;oEAAoC;oEACjD,MAAM,UAAU;;;;;;;;;;;;;kEAGpB,6LAAC;wDAAE,WAAU;;0EACZ,6LAAC;gEAAK,WAAU;0EAA4B;;;;;;4DAEpC;4DACP,IAAI,KAAK,MAAM,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;0CAMjD,6LAAC;gCAAI,WAAU;;kDACd,6LAAC;wCACA,WAAW,AAAC,4CAEV,OAFqD,eACtD,MAAM,MAAM;kDAGZ,MAAM,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,MAAM,MAAM,CAAC,KAAK,CAAC;;;;;;kDAG5D,6LAAC;wCAAI,WAAU;;4CAAwC;4CACzC,MAAM,EAAE,CAAC,KAAK,CAAC,CAAC;;;;;;;;;;;;;;uBAjF1B,MAAM,EAAE;;;;;;;;;;;;;;;;AA0FpB;GAnJwB;KAAA", "debugId": null}}]}