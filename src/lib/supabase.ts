// Simple data types for our marketplace
export interface Product {
	id: string;
	name: string;
	description: string;
	price: number;
	image_url?: string;
	created_at: string;
}

export interface CustomerOrder {
	id: string;
	customerName: string;
	address: string;
	phoneNo: string;
	pinCode: string;
	productName: string;
	productPrice: number;
	quantity: number;
	totalPrice: number;
	orderDate: string;
	status: "pending" | "completed" | "cancelled";
}

// Simple in-memory storage (you can replace this with a file or simple database later)
let products: Product[] = [
	{
		id: "1",
		name: "Sample Product",
		description: "This is a sample product to get you started",
		price: 29.99,
		image_url:
			"https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400",
		created_at: new Date().toISOString(),
	},
];

let orders: CustomerOrder[] = [];

// Product functions
export const getProducts = (): Product[] => {
	return products;
};

export const addProduct = (
	product: Omit<Product, "id" | "created_at">
): Product => {
	const newProduct: Product = {
		...product,
		id: Date.now().toString(),
		created_at: new Date().toISOString(),
	};
	products.push(newProduct);
	return newProduct;
};

export const getProductById = (id: string): Product | undefined => {
	return products.find((p) => p.id === id);
};

// Order functions
export const addOrder = (
	order: Omit<CustomerOrder, "id" | "orderDate">
): CustomerOrder => {
	const newOrder: CustomerOrder = {
		...order,
		id: Date.now().toString(),
		orderDate: new Date().toISOString(),
	};
	orders.push(newOrder);

	// Log order details to console for easy viewing
	console.log("🛒 NEW ORDER RECEIVED:");
	console.log("Customer:", newOrder.customerName);
	console.log("Phone:", newOrder.phoneNo);
	console.log("Address:", newOrder.address);
	console.log("Pin Code:", newOrder.pinCode);
	console.log("Product:", newOrder.productName);
	console.log("Price:", `$${newOrder.productPrice}`);
	console.log("Quantity:", newOrder.quantity);
	console.log("Total:", `$${newOrder.totalPrice}`);
	console.log("Date:", new Date(newOrder.orderDate).toLocaleString());
	console.log("-------------------");

	return newOrder;
};

export const getOrders = (): CustomerOrder[] => {
	return orders;
};
