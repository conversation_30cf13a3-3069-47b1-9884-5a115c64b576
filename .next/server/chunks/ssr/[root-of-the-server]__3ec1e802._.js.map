{"version": 3, "sources": [], "sections": [{"offset": {"line": 10, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/shreeji/marketplace/src/lib/supabase.ts"], "sourcesContent": ["// Simple data types for our marketplace\nexport interface Product {\n\tid: string;\n\tname: string;\n\tdescription: string;\n\tprice: number;\n\timage_url?: string;\n\tcreated_at: string;\n}\n\nexport interface CustomerOrder {\n\tid: string;\n\tcustomerName: string;\n\taddress: string;\n\tphoneNo: string;\n\tpinCode: string;\n\tproductName: string;\n\tproductPrice: number;\n\tquantity: number;\n\ttotalPrice: number;\n\torderDate: string;\n\tstatus: \"pending\" | \"completed\" | \"cancelled\";\n}\n\n// Simple in-memory storage (you can replace this with a file or simple database later)\nlet products: Product[] = [\n\t{\n\t\tid: \"1\",\n\t\tname: \"Sample Product\",\n\t\tdescription: \"This is a sample product to get you started\",\n\t\tprice: 29.99,\n\t\timage_url:\n\t\t\t\"https://images.unsplash.com/photo-1523275335684-37898b6baf30?w=400\",\n\t\tcreated_at: new Date().toISOString(),\n\t},\n];\n\nlet orders: CustomerOrder[] = [];\n\n// Product functions\nexport const getProducts = (): Product[] => {\n\treturn products;\n};\n\nexport const addProduct = (\n\tproduct: Omit<Product, \"id\" | \"created_at\">\n): Product => {\n\tconst newProduct: Product = {\n\t\t...product,\n\t\tid: Date.now().toString(),\n\t\tcreated_at: new Date().toISOString(),\n\t};\n\tproducts.push(newProduct);\n\treturn newProduct;\n};\n\nexport const getProductById = (id: string): Product | undefined => {\n\treturn products.find((p) => p.id === id);\n};\n\n// Order functions\nexport const addOrder = (\n\torder: Omit<CustomerOrder, \"id\" | \"orderDate\">\n): CustomerOrder => {\n\tconst newOrder: CustomerOrder = {\n\t\t...order,\n\t\tid: Date.now().toString(),\n\t\torderDate: new Date().toISOString(),\n\t};\n\torders.push(newOrder);\n\n\t// Log order details to console for easy viewing\n\tconsole.log(\"🛒 NEW ORDER RECEIVED:\");\n\tconsole.log(\"Customer:\", newOrder.customerName);\n\tconsole.log(\"Phone:\", newOrder.phoneNo);\n\tconsole.log(\"Address:\", newOrder.address);\n\tconsole.log(\"Pin Code:\", newOrder.pinCode);\n\tconsole.log(\"Product:\", newOrder.productName);\n\tconsole.log(\"Price:\", `$${newOrder.productPrice}`);\n\tconsole.log(\"Quantity:\", newOrder.quantity);\n\tconsole.log(\"Total:\", `$${newOrder.totalPrice}`);\n\tconsole.log(\"Date:\", new Date(newOrder.orderDate).toLocaleString());\n\tconsole.log(\"-------------------\");\n\n\treturn newOrder;\n};\n\nexport const getOrders = (): CustomerOrder[] => {\n\treturn orders;\n};\n"], "names": [], "mappings": "AAAA,wCAAwC;;;;;;;;;;;;;AAwBxC,uFAAuF;AACvF,IAAI,WAAsB;IACzB;QACC,IAAI;QACJ,MAAM;QACN,aAAa;QACb,OAAO;QACP,WACC;QACD,YAAY,IAAI,OAAO,WAAW;IACnC;CACA;AAED,IAAI,SAA0B,EAAE;AAGzB,MAAM,cAAc;IAC1B,OAAO;AACR;AAEO,MAAM,aAAa,CACzB;IAEA,MAAM,aAAsB;QAC3B,GAAG,OAAO;QACV,IAAI,KAAK,GAAG,GAAG,QAAQ;QACvB,YAAY,IAAI,OAAO,WAAW;IACnC;IACA,SAAS,IAAI,CAAC;IACd,OAAO;AACR;AAEO,MAAM,iBAAiB,CAAC;IAC9B,OAAO,SAAS,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;AACtC;AAGO,MAAM,WAAW,CACvB;IAEA,MAAM,WAA0B;QAC/B,GAAG,KAAK;QACR,IAAI,KAAK,GAAG,GAAG,QAAQ;QACvB,WAAW,IAAI,OAAO,WAAW;IAClC;IACA,OAAO,IAAI,CAAC;IAEZ,gDAAgD;IAChD,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,aAAa,SAAS,YAAY;IAC9C,QAAQ,GAAG,CAAC,UAAU,SAAS,OAAO;IACtC,QAAQ,GAAG,CAAC,YAAY,SAAS,OAAO;IACxC,QAAQ,GAAG,CAAC,aAAa,SAAS,OAAO;IACzC,QAAQ,GAAG,CAAC,YAAY,SAAS,WAAW;IAC5C,QAAQ,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,SAAS,YAAY,EAAE;IACjD,QAAQ,GAAG,CAAC,aAAa,SAAS,QAAQ;IAC1C,QAAQ,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,SAAS,UAAU,EAAE;IAC/C,QAAQ,GAAG,CAAC,SAAS,IAAI,KAAK,SAAS,SAAS,EAAE,cAAc;IAChE,QAAQ,GAAG,CAAC;IAEZ,OAAO;AACR;AAEO,MAAM,YAAY;IACxB,OAAO;AACR", "debugId": null}}, {"offset": {"line": 96, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/shreeji/marketplace/src/components/Navbar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { supabase } from '@/lib/supabase'\nimport { User } from '@supabase/supabase-js'\nimport Link from 'next/link'\n\nexport default function Navbar() {\n  const [user, setUser] = useState<User | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    // Get initial user\n    const getUser = async () => {\n      const { data: { user } } = await supabase.auth.getUser()\n      setUser(user)\n      setLoading(false)\n    }\n    \n    getUser()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      (event, session) => {\n        setUser(session?.user ?? null)\n        setLoading(false)\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [])\n\n  const handleSignOut = async () => {\n    await supabase.auth.signOut()\n  }\n\n  return (\n    <nav className=\"bg-white shadow-sm border-b\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex justify-between items-center h-16\">\n          <Link href=\"/\" className=\"text-xl font-bold text-gray-900\">\n            Simple Marketplace\n          </Link>\n          \n          <div className=\"flex items-center space-x-4\">\n            {loading ? (\n              <div className=\"text-gray-500\">Loading...</div>\n            ) : user ? (\n              <>\n                <Link \n                  href=\"/products\" \n                  className=\"text-gray-700 hover:text-gray-900\"\n                >\n                  Browse Products\n                </Link>\n                <Link \n                  href=\"/add-product\" \n                  className=\"text-gray-700 hover:text-gray-900\"\n                >\n                  Sell Product\n                </Link>\n                <Link \n                  href=\"/orders\" \n                  className=\"text-gray-700 hover:text-gray-900\"\n                >\n                  My Orders\n                </Link>\n                <span className=\"text-gray-600 text-sm\">\n                  {user.email}\n                </span>\n                <button\n                  onClick={handleSignOut}\n                  className=\"bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700\"\n                >\n                  Sign Out\n                </button>\n              </>\n            ) : (\n              <Link \n                href=\"/auth\" \n                className=\"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700\"\n              >\n                Sign In\n              </Link>\n            )}\n          </div>\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,iNAAQ,EAAc;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IAEvC,IAAA,kNAAS,EAAC;QACR,mBAAmB;QACnB,MAAM,UAAU;YACd,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,kIAAQ,CAAC,IAAI,CAAC,OAAO;YACtD,QAAQ;YACR,WAAW;QACb;QAEA;QAEA,0BAA0B;QAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,kIAAQ,CAAC,IAAI,CAAC,iBAAiB,CAChE,CAAC,OAAO;YACN,QAAQ,SAAS,QAAQ;YACzB,WAAW;QACb;QAGF,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,MAAM,kIAAQ,CAAC,IAAI,CAAC,OAAO;IAC7B;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,uKAAI;wBAAC,MAAK;wBAAI,WAAU;kCAAkC;;;;;;kCAI3D,8OAAC;wBAAI,WAAU;kCACZ,wBACC,8OAAC;4BAAI,WAAU;sCAAgB;;;;;mCAC7B,qBACF;;8CACE,8OAAC,uKAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,uKAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,uKAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCAAK,WAAU;8CACb,KAAK,KAAK;;;;;;8CAEb,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;yDAKH,8OAAC,uKAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}]}