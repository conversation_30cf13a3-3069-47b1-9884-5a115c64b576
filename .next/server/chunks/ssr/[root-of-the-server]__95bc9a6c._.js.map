{"version": 3, "sources": [], "sections": [{"offset": {"line": 28, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/shreeji/marketplace/src/components/Navbar.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\n\nexport default function Navbar() {\n\treturn (\n\t\t<nav className=\"spiritual-gradient shadow-lg border-b-2 border-orange-300 divine-glow\">\n\t\t\t<div className=\"container mx-auto px-4\">\n\t\t\t\t<div className=\"flex justify-between items-center h-16\">\n\t\t\t\t\t<Link href=\"/\" className=\"text-2xl font-bold text-white sacred-text\">\n\t\t\t\t\t\t🕉️ Shri Hit Sakhi 🕉️\n\t\t\t\t\t</Link>\n\n\t\t\t\t\t<div className=\"flex items-center space-x-6\">\n\t\t\t\t\t\t<Link\n\t\t\t\t\t\t\thref=\"/products\"\n\t\t\t\t\t\t\tclassName=\"text-white hover:text-yellow-200 font-semibold transition-colors duration-200\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t🛍️ Shop\n\t\t\t\t\t\t</Link>\n\t\t\t\t\t\t<Link\n\t\t\t\t\t\t\thref=\"/orders\"\n\t\t\t\t\t\t\tclassName=\"text-white hover:text-yellow-200 font-semibold transition-colors duration-200\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t📋 Orders\n\t\t\t\t\t\t</Link>\n\t\t\t\t\t\t<Link\n\t\t\t\t\t\t\thref=\"/add-product\"\n\t\t\t\t\t\t\tclassName=\"text-orange-100 hover:text-yellow-200 text-sm font-medium transition-colors duration-200\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t⚙️ Admin\n\t\t\t\t\t\t</Link>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</nav>\n\t);\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAIe,SAAS;IACvB,qBACC,8OAAC;QAAI,WAAU;kBACd,cAAA,8OAAC;YAAI,WAAU;sBACd,cAAA,8OAAC;gBAAI,WAAU;;kCACd,8OAAC,uKAAI;wBAAC,MAAK;wBAAI,WAAU;kCAA4C;;;;;;kCAIrE,8OAAC;wBAAI,WAAU;;0CACd,8OAAC,uKAAI;gCACJ,MAAK;gCACL,WAAU;0CACV;;;;;;0CAGD,8OAAC,uKAAI;gCACJ,MAAK;gCACL,WAAU;0CACV;;;;;;0CAGD,8OAAC,uKAAI;gCACJ,MAAK;gCACL,WAAU;0CACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQP", "debugId": null}}]}