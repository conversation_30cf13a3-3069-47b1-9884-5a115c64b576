"use client";

import { useState, useEffect } from "react";
import { getProducts, Product } from "@/lib/supabase";
import Link from "next/link";

export default function ProductsPage() {
	const [products, setProducts] = useState<Product[]>([]);
	const [loading, setLoading] = useState(true);
	const [searchTerm, setSearchTerm] = useState("");

	useEffect(() => {
		fetchProducts();
	}, []);

	const fetchProducts = () => {
		try {
			const allProducts = getProducts();
			setProducts(allProducts);
		} catch (error) {
			console.error("Error fetching products:", error);
		} finally {
			setLoading(false);
		}
	};

	const filteredProducts = products.filter(
		(product) =>
			product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			product.description?.toLowerCase().includes(searchTerm.toLowerCase())
	);

	return (
		<div className="space-y-8">
			<div className="text-center">
				<h1 className="text-4xl font-bold text-orange-800 sacred-text">
					Sacred Collection from Vrindavan
				</h1>
				<p className="text-lg text-orange-600 mt-2">
					Divine spiritual items for our beloved bhakts
				</p>
			</div>

			{/* Search */}
			<div className="max-w-md mx-auto">
				<div className="relative">
					<input
						type="text"
						placeholder="🔍 Search spiritual items..."
						value={searchTerm}
						onChange={(e) => setSearchTerm(e.target.value)}
						className="w-full px-6 py-3 border-2 border-orange-300 rounded-full focus:outline-none focus:ring-2 focus:ring-orange-500 text-orange-900 bg-white shadow-lg font-medium"
					/>
				</div>
			</div>

			{/* Products Grid */}
			{loading ? (
				<div className="text-center py-8">
					<div className="text-gray-500">Loading products...</div>
				</div>
			) : filteredProducts.length === 0 ? (
				<div className="text-center py-8">
					<div className="text-gray-500 mb-4">
						{searchTerm
							? "No products found matching your search."
							: "No products available yet."}
					</div>
					<Link
						href="/add-product"
						className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700"
					>
						Add the first product
					</Link>
				</div>
			) : (
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
					{filteredProducts.map((product) => (
						<div
							key={product.id}
							className="bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow"
						>
							{product.image_url && (
								<img
									src={product.image_url}
									alt={product.name}
									className="w-full h-48 object-cover rounded-t-lg"
								/>
							)}
							<div className="p-4">
								<h3 className="font-semibold text-gray-900 mb-2">
									{product.name}
								</h3>
								<p className="text-gray-600 text-sm mb-3 line-clamp-3">
									{product.description}
								</p>
								<div className="flex justify-between items-center">
									<span className="text-lg font-bold text-green-600">
										${product.price}
									</span>
									<Link
										href={`/products/${product.id}`}
										className="bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700"
									>
										View Details
									</Link>
								</div>
							</div>
						</div>
					))}
				</div>
			)}
		</div>
	);
}
