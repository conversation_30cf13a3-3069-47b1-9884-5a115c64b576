{"version": 3, "sources": [], "sections": [{"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/shreeji/marketplace/src/lib/supabase.ts"], "sourcesContent": ["import { createClient } from '@supabase/supabase-js'\n\nconst supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!\nconst supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!\n\nexport const supabase = createClient(supabaseUrl, supabaseAnonKey)\n\n// Database Types\nexport interface User {\n  id: string\n  email: string\n  created_at: string\n}\n\nexport interface Product {\n  id: string\n  name: string\n  description: string\n  price: number\n  image_url?: string\n  user_id: string\n  created_at: string\n}\n\nexport interface Order {\n  id: string\n  user_id: string\n  product_id: string\n  quantity: number\n  total_price: number\n  status: 'pending' | 'completed' | 'cancelled'\n  created_at: string\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM;AACN,MAAM;AAEC,MAAM,WAAW,IAAA,uMAAY,EAAC,aAAa", "debugId": null}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/shreeji/marketplace/src/components/Navbar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { supabase } from '@/lib/supabase'\nimport { User } from '@supabase/supabase-js'\nimport Link from 'next/link'\n\nexport default function Navbar() {\n  const [user, setUser] = useState<User | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    // Get initial user\n    const getUser = async () => {\n      const { data: { user } } = await supabase.auth.getUser()\n      setUser(user)\n      setLoading(false)\n    }\n    \n    getUser()\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      (event, session) => {\n        setUser(session?.user ?? null)\n        setLoading(false)\n      }\n    )\n\n    return () => subscription.unsubscribe()\n  }, [])\n\n  const handleSignOut = async () => {\n    await supabase.auth.signOut()\n  }\n\n  return (\n    <nav className=\"bg-white shadow-sm border-b\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex justify-between items-center h-16\">\n          <Link href=\"/\" className=\"text-xl font-bold text-gray-900\">\n            Simple Marketplace\n          </Link>\n          \n          <div className=\"flex items-center space-x-4\">\n            {loading ? (\n              <div className=\"text-gray-500\">Loading...</div>\n            ) : user ? (\n              <>\n                <Link \n                  href=\"/products\" \n                  className=\"text-gray-700 hover:text-gray-900\"\n                >\n                  Browse Products\n                </Link>\n                <Link \n                  href=\"/add-product\" \n                  className=\"text-gray-700 hover:text-gray-900\"\n                >\n                  Sell Product\n                </Link>\n                <Link \n                  href=\"/orders\" \n                  className=\"text-gray-700 hover:text-gray-900\"\n                >\n                  My Orders\n                </Link>\n                <span className=\"text-gray-600 text-sm\">\n                  {user.email}\n                </span>\n                <button\n                  onClick={handleSignOut}\n                  className=\"bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700\"\n                >\n                  Sign Out\n                </button>\n              </>\n            ) : (\n              <Link \n                href=\"/auth\" \n                className=\"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700\"\n              >\n                Sign In\n              </Link>\n            )}\n          </div>\n        </div>\n      </div>\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOe,SAAS;IACtB,MAAM,CAAC,MAAM,QAAQ,GAAG,IAAA,iNAAQ,EAAc;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,iNAAQ,EAAC;IAEvC,IAAA,kNAAS,EAAC;QACR,mBAAmB;QACnB,MAAM,UAAU;YACd,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,GAAG,MAAM,kIAAQ,CAAC,IAAI,CAAC,OAAO;YACtD,QAAQ;YACR,WAAW;QACb;QAEA;QAEA,0BAA0B;QAC1B,MAAM,EAAE,MAAM,EAAE,YAAY,EAAE,EAAE,GAAG,kIAAQ,CAAC,IAAI,CAAC,iBAAiB,CAChE,CAAC,OAAO;YACN,QAAQ,SAAS,QAAQ;YACzB,WAAW;QACb;QAGF,OAAO,IAAM,aAAa,WAAW;IACvC,GAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,MAAM,kIAAQ,CAAC,IAAI,CAAC,OAAO;IAC7B;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,uKAAI;wBAAC,MAAK;wBAAI,WAAU;kCAAkC;;;;;;kCAI3D,8OAAC;wBAAI,WAAU;kCACZ,wBACC,8OAAC;4BAAI,WAAU;sCAAgB;;;;;mCAC7B,qBACF;;8CACE,8OAAC,uKAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,uKAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC,uKAAI;oCACH,MAAK;oCACL,WAAU;8CACX;;;;;;8CAGD,8OAAC;oCAAK,WAAU;8CACb,KAAK,KAAK;;;;;;8CAEb,8OAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;yDAKH,8OAAC,uKAAI;4BACH,MAAK;4BACL,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf", "debugId": null}}]}