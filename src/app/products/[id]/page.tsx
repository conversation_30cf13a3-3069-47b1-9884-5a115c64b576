"use client";

import { useState, useEffect, use } from "react";
import { getProductById, addOrder, Product } from "@/lib/supabase";
import Link from "next/link";
import { WHATSAPP_NUMBER } from "@/config/whatsapp";

interface ProductDetailPageProps {
	params: Promise<{ id: string }>;
}

export default function ProductDetailPage({ params }: ProductDetailPageProps) {
	// Unwrap the params Promise
	const resolvedParams = use(params);

	const [product, setProduct] = useState<Product | null>(null);
	const [loading, setLoading] = useState(true);
	const [showOrderForm, setShowOrderForm] = useState(false);
	const [orderLoading, setOrderLoading] = useState(false);
	const [message, setMessage] = useState("");

	// Customer form fields
	const [customerName, setCustomerName] = useState("");
	const [address, setAddress] = useState("");
	const [phoneNo, setPhoneNo] = useState("");
	const [pinCode, setPinCode] = useState("");
	const [quantity, setQuantity] = useState(1);

	useEffect(() => {
		fetchProduct();
	}, [resolvedParams.id]);

	const fetchProduct = () => {
		try {
			const foundProduct = getProductById(resolvedParams.id);
			setProduct(foundProduct || null);
			if (!foundProduct) {
				setMessage("Product not found");
			}
		} catch (error) {
			console.error("Error fetching product:", error);
			setMessage("Product not found");
		} finally {
			setLoading(false);
		}
	};

	const handleOrderSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		if (!product) return;

		setOrderLoading(true);
		setMessage("");

		try {
			// Save order locally for your records
			addOrder({
				customerName,
				address,
				phoneNo,
				pinCode,
				productName: product.name,
				productPrice: product.price,
				quantity,
				totalPrice: product.price * quantity,
				status: "pending",
			});

			// Create WhatsApp message
			const whatsappMessage = `🙏 *Namaste from ${customerName}* 🙏

*Sacred Item Order:*
📿 Item: ${product.name}
💰 Price: ₹${product.price} each
🔢 Quantity: ${quantity}
💵 Total: ₹${(product.price * quantity).toFixed(2)}

*Devotee Details:*
👤 Name: ${customerName}
📱 Phone: ${phoneNo}
🏠 Address: ${address}
📍 Pin Code: ${pinCode}

*Ready to proceed with payment and delivery arrangements.*

_Jai Shri Krishna!_ 🕉️`;

			// Encode message for WhatsApp URL
			const encodedMessage = encodeURIComponent(whatsappMessage);
			const whatsappURL = `https://wa.me/${WHATSAPP_NUMBER}?text=${encodedMessage}`;

			// Open WhatsApp
			window.open(whatsappURL, "_blank");

			setMessage(
				"🙏 Order details sent to WhatsApp! Please complete your order there."
			);

			// Reset form
			setCustomerName("");
			setAddress("");
			setPinCode("");
			setQuantity(1);
			setShowOrderForm(false);
		} catch (error: any) {
			setMessage(`Error placing order: ${error.message}`);
		} finally {
			setOrderLoading(false);
		}
	};

	if (loading) {
		return (
			<div className="text-center py-8">
				<div className="text-gray-500">Loading product...</div>
			</div>
		);
	}

	if (!product) {
		return (
			<div className="text-center py-8">
				<div className="text-gray-500 mb-4">Product not found</div>
				<Link href="/products" className="text-blue-600 hover:text-blue-500">
					Back to Products
				</Link>
			</div>
		);
	}

	return (
		<div className="max-w-4xl mx-auto">
			<div className="mb-8">
				<Link
					href="/products"
					className="text-orange-600 hover:text-orange-500 font-semibold"
				>
					🔙 Back to Sacred Collection
				</Link>
			</div>

			<div className="bg-gradient-to-br from-white to-orange-50 rounded-2xl shadow-xl border-2 border-orange-200 overflow-hidden divine-glow">
				<div className="md:flex">
					{product.image_url && (
						<div className="md:w-1/2">
							<img
								src={product.image_url}
								alt={product.name}
								className="w-full h-64 md:h-full object-cover"
							/>
						</div>
					)}

					<div className={`p-8 ${product.image_url ? "md:w-1/2" : "w-full"}`}>
						<h1 className="text-4xl font-bold text-orange-800 mb-6 sacred-text">
							{product.name}
						</h1>

						<div className="text-3xl font-bold text-orange-600 mb-8">
							₹{product.price}
						</div>

						{product.description && (
							<div className="mb-8">
								<h3 className="text-xl font-bold text-orange-800 mb-3">
									Sacred Description
								</h3>
								<p className="text-orange-700 whitespace-pre-wrap leading-relaxed">
									{product.description}
								</p>
							</div>
						)}

						<div className="space-y-4">
							{!showOrderForm ? (
								<button
									onClick={() => setShowOrderForm(true)}
									className="w-full spiritual-gradient text-white py-4 px-8 rounded-full hover:scale-105 transform transition-all duration-300 font-bold text-lg shadow-lg divine-glow"
								>
									📱 Order via WhatsApp 📱
								</button>
							) : (
								<form
									onSubmit={handleOrderSubmit}
									className="space-y-6 bg-orange-50 p-6 rounded-xl border-2 border-orange-200"
								>
									<h3 className="text-xl font-bold text-orange-800 text-center sacred-text">
										🙏 Devotee Details 🙏
									</h3>

									<div>
										<label className="block text-sm font-bold text-orange-800 mb-2">
											Devotee Name *
										</label>
										<input
											type="text"
											value={customerName}
											onChange={(e) => setCustomerName(e.target.value)}
											required
											className="w-full px-4 py-3 border-2 border-orange-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500 text-orange-900 bg-white shadow-md font-medium"
										/>
									</div>

									<div>
										<label className="block text-sm font-medium text-gray-700 mb-1">
											Address *
										</label>
										<textarea
											value={address}
											onChange={(e) => setAddress(e.target.value)}
											required
											rows={3}
											className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 text-gray-900 bg-white"
										/>
									</div>

									<div className="grid grid-cols-2 gap-4">
										<div>
											<label className="block text-sm font-medium text-gray-700 mb-1">
												Phone Number *
											</label>
											<input
												type="tel"
												value={phoneNo}
												onChange={(e) => setPhoneNo(e.target.value)}
												required
												className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 text-gray-900 bg-white"
											/>
										</div>

										<div>
											<label className="block text-sm font-medium text-gray-700 mb-1">
												Pin Code *
											</label>
											<input
												type="text"
												value={pinCode}
												onChange={(e) => setPinCode(e.target.value)}
												required
												className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 text-gray-900 bg-white"
											/>
										</div>
									</div>

									<div>
										<label className="block text-sm font-medium text-gray-700 mb-1">
											Quantity
										</label>
										<input
											type="number"
											min="1"
											value={quantity}
											onChange={(e) => setQuantity(parseInt(e.target.value))}
											className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 text-gray-900 bg-white"
										/>
									</div>

									<div className="bg-gradient-to-r from-orange-100 to-yellow-100 p-4 rounded-lg border-2 border-orange-300">
										<div className="flex justify-between text-xl font-bold text-orange-800">
											<span>Total Amount:</span>
											<span>₹{(product.price * quantity).toFixed(2)}</span>
										</div>
									</div>

									<div className="flex space-x-4">
										<button
											type="button"
											onClick={() => setShowOrderForm(false)}
											className="flex-1 bg-orange-200 text-orange-800 py-3 px-4 rounded-full hover:bg-orange-300 font-semibold transition-colors duration-200"
										>
											Cancel
										</button>
										<button
											type="submit"
											disabled={orderLoading}
											className="flex-1 spiritual-gradient text-white py-3 px-4 rounded-full hover:scale-105 transform transition-all duration-300 font-bold shadow-lg disabled:opacity-50"
										>
											{orderLoading
												? "📱 Opening WhatsApp..."
												: "📱 Send to WhatsApp"}
										</button>
									</div>
								</form>
							)}

							{message && (
								<div
									className={`p-3 rounded-md text-sm ${
										message.includes("Error")
											? "bg-red-100 text-red-700"
											: "bg-green-100 text-green-700"
									}`}
								>
									{message}
								</div>
							)}
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
