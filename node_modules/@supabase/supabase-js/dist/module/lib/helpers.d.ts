import { SupabaseClientOptions } from './types';
export declare function uuid(): string;
export declare function ensureTrailingSlash(url: string): string;
export declare const isBrowser: () => boolean;
export declare function applySettingDefaults<Database = any, SchemaName extends string & keyof Database = 'public' extends keyof Database ? 'public' : string & keyof Database>(options: SupabaseClientOptions<SchemaName>, defaults: SupabaseClientOptions<any>): Required<SupabaseClientOptions<SchemaName>>;
/**
 * Validates a Supabase client URL
 *
 * @param {string} supabaseUrl - The Supabase client URL string.
 * @returns {URL} - The validated base URL.
 * @throws {Error}
 */
export declare function validateSupabaseUrl(supabaseUrl: string): URL;
//# sourceMappingURL=helpers.d.ts.map