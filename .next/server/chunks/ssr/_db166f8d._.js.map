{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/shreeji/marketplace/src/components/Navbar.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Navbar.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Navbar.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAA6R,GAC1T,2DACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 18, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/shreeji/marketplace/src/components/Navbar.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/Navbar.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/Navbar.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,wQAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAyQ,GACtS,uCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 32, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 40, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/shreeji/marketplace/src/app/layout.tsx"], "sourcesContent": ["import type { Metadata } from \"next\";\nimport \"./globals.css\";\nimport Navbar from \"@/components/Navbar\";\n\nexport const metadata: Metadata = {\n\ttitle: \"Radha Nam - Sacred Items from Vrindavan\",\n\tdescription:\n\t\t\"Divine spiritual items and sacred products from Vrindavan for devotees\",\n\ticons: {\n\t\ticon: \"/Radha Nam Logo.jpg\",\n\t\tshortcut: \"/Radha Nam Logo.jpg\",\n\t\tapple: \"/Radha Nam Logo.jpg\",\n\t},\n};\n\nexport default function RootLayout({\n\tchildren,\n}: Readonly<{\n\tchildren: React.ReactNode;\n}>) {\n\treturn (\n\t\t<html lang=\"en\">\n\t\t\t<body className=\"min-h-screen spiritual-gradient-light\">\n\t\t\t\t<Navbar />\n\t\t\t\t<main className=\"container mx-auto px-4 py-8\">{children}</main>\n\t\t\t</body>\n\t\t</html>\n\t);\n}\n"], "names": [], "mappings": ";;;;;;;AAEA;;;;AAEO,MAAM,WAAqB;IACjC,OAAO;IACP,aACC;IACD,OAAO;QACN,MAAM;QACN,UAAU;QACV,OAAO;IACR;AACD;AAEe,SAAS,WAAW,EAClC,QAAQ,EAGP;IACD,qBACC,8OAAC;QAAK,MAAK;kBACV,cAAA,8OAAC;YAAK,WAAU;;8BACf,8OAAC,uIAAM;;;;;8BACP,8OAAC;oBAAK,WAAU;8BAA+B;;;;;;;;;;;;;;;;;AAInD", "debugId": null}}, {"offset": {"line": 95, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/shreeji/marketplace/node_modules/next/src/server/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = (\n  require('../../module.compiled') as typeof import('../../module.compiled')\n).vendored['react-rsc']!.ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": "AAAAA,OAAOC,OAAO,GACZC,QAAQ,4HACRC,QAAQ,CAAC,YAAY,CAAEC,kBAAkB", "ignoreList": [0], "debugId": null}}]}