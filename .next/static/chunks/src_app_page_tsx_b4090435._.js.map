{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/shreeji/marketplace/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { getProducts, Product } from \"@/lib/supabase\";\nimport Link from \"next/link\";\n\nexport default function Home() {\n\tconst [products, setProducts] = useState<Product[]>([]);\n\tconst [loading, setLoading] = useState(true);\n\tconst [searchTerm, setSearchTerm] = useState(\"\");\n\n\tuseEffect(() => {\n\t\tfetchProducts();\n\t}, []);\n\n\tconst fetchProducts = () => {\n\t\ttry {\n\t\t\tconst allProducts = getProducts();\n\t\t\tsetProducts(allProducts);\n\t\t} catch (error) {\n\t\t\tconsole.error(\"Error fetching products:\", error);\n\t\t} finally {\n\t\t\tsetLoading(false);\n\t\t}\n\t};\n\n\tconst filteredProducts = products.filter(\n\t\t(product) =>\n\t\t\tproduct.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n\t\t\tproduct.description?.toLowerCase().includes(searchTerm.toLowerCase())\n\t);\n\n\treturn (\n\t\t<div className=\"space-y-8\">\n\t\t\t{/* Hero Section */}\n\t\t\t<div className=\"text-center py-12 bg-white rounded-lg shadow-sm\">\n\t\t\t\t<h1 className=\"text-4xl font-bold text-gray-900 mb-4\">\n\t\t\t\t\tWelcome to Simple Marketplace\n\t\t\t\t</h1>\n\t\t\t\t<p className=\"text-xl text-gray-600 mb-8\">\n\t\t\t\t\tBuy and sell products easily in our simple, clean marketplace\n\t\t\t\t</p>\n\t\t\t\t<div className=\"flex justify-center space-x-4\">\n\t\t\t\t\t<Link\n\t\t\t\t\t\thref=\"/products\"\n\t\t\t\t\t\tclassName=\"bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700\"\n\t\t\t\t\t>\n\t\t\t\t\t\tBrowse All Products\n\t\t\t\t\t</Link>\n\t\t\t\t\t<Link\n\t\t\t\t\t\thref=\"/add-product\"\n\t\t\t\t\t\tclassName=\"bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700\"\n\t\t\t\t\t>\n\t\t\t\t\t\tStart Selling\n\t\t\t\t\t</Link>\n\t\t\t\t</div>\n\t\t\t</div>\n\n\t\t\t{/* Search */}\n\t\t\t<div className=\"max-w-md mx-auto\">\n\t\t\t\t<input\n\t\t\t\t\ttype=\"text\"\n\t\t\t\t\tplaceholder=\"Search products...\"\n\t\t\t\t\tvalue={searchTerm}\n\t\t\t\t\tonChange={(e) => setSearchTerm(e.target.value)}\n\t\t\t\t\tclassName=\"w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500\"\n\t\t\t\t/>\n\t\t\t</div>\n\n\t\t\t{/* Recent Products */}\n\t\t\t<div>\n\t\t\t\t<h2 className=\"text-2xl font-bold text-gray-900 mb-6\">\n\t\t\t\t\tRecent Products\n\t\t\t\t</h2>\n\n\t\t\t\t{loading ? (\n\t\t\t\t\t<div className=\"text-center py-8\">\n\t\t\t\t\t\t<div className=\"text-gray-500\">Loading products...</div>\n\t\t\t\t\t</div>\n\t\t\t\t) : filteredProducts.length === 0 ? (\n\t\t\t\t\t<div className=\"text-center py-8\">\n\t\t\t\t\t\t<div className=\"text-gray-500\">\n\t\t\t\t\t\t\t{searchTerm\n\t\t\t\t\t\t\t\t? \"No products found matching your search.\"\n\t\t\t\t\t\t\t\t: \"No products available yet.\"}\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<Link\n\t\t\t\t\t\t\thref=\"/add-product\"\n\t\t\t\t\t\t\tclassName=\"text-blue-600 hover:text-blue-500 mt-2 inline-block\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\tBe the first to add a product!\n\t\t\t\t\t\t</Link>\n\t\t\t\t\t</div>\n\t\t\t\t) : (\n\t\t\t\t\t<div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\">\n\t\t\t\t\t\t{filteredProducts.map((product) => (\n\t\t\t\t\t\t\t<div\n\t\t\t\t\t\t\t\tkey={product.id}\n\t\t\t\t\t\t\t\tclassName=\"bg-white rounded-lg shadow-sm border hover:shadow-md transition-shadow\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{product.image_url && (\n\t\t\t\t\t\t\t\t\t<img\n\t\t\t\t\t\t\t\t\t\tsrc={product.image_url}\n\t\t\t\t\t\t\t\t\t\talt={product.name}\n\t\t\t\t\t\t\t\t\t\tclassName=\"w-full h-48 object-cover rounded-t-lg\"\n\t\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t)}\n\t\t\t\t\t\t\t\t<div className=\"p-4\">\n\t\t\t\t\t\t\t\t\t<h3 className=\"font-semibold text-gray-900 mb-2\">\n\t\t\t\t\t\t\t\t\t\t{product.name}\n\t\t\t\t\t\t\t\t\t</h3>\n\t\t\t\t\t\t\t\t\t<p className=\"text-gray-600 text-sm mb-3 line-clamp-2\">\n\t\t\t\t\t\t\t\t\t\t{product.description}\n\t\t\t\t\t\t\t\t\t</p>\n\t\t\t\t\t\t\t\t\t<div className=\"flex justify-between items-center\">\n\t\t\t\t\t\t\t\t\t\t<span className=\"text-lg font-bold text-green-600\">\n\t\t\t\t\t\t\t\t\t\t\t${product.price}\n\t\t\t\t\t\t\t\t\t\t</span>\n\t\t\t\t\t\t\t\t\t\t<Link\n\t\t\t\t\t\t\t\t\t\t\thref={`/products/${product.id}`}\n\t\t\t\t\t\t\t\t\t\t\tclassName=\"bg-blue-600 text-white px-3 py-1 rounded text-sm hover:bg-blue-700\"\n\t\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t\tView Details\n\t\t\t\t\t\t\t\t\t\t</Link>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t))}\n\t\t\t\t\t</div>\n\t\t\t\t)}\n\t\t\t</div>\n\t\t</div>\n\t);\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACvB,MAAM,CAAC,UAAU,YAAY,GAAG,IAAA,yKAAQ,EAAY,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,IAAA,yKAAQ,EAAC;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,IAAA,yKAAQ,EAAC;IAE7C,IAAA,0KAAS;0BAAC;YACT;QACD;yBAAG,EAAE;IAEL,MAAM,gBAAgB;QACrB,IAAI;YACH,MAAM,cAAc,IAAA,wIAAW;YAC/B,YAAY;QACb,EAAE,OAAO,OAAO;YACf,QAAQ,KAAK,CAAC,4BAA4B;QAC3C,SAAU;YACT,WAAW;QACZ;IACD;IAEA,MAAM,mBAAmB,SAAS,MAAM,CACvC,CAAC;YAEA;eADA,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,SAC1D,uBAAA,QAAQ,WAAW,cAAnB,2CAAA,qBAAqB,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;;IAGpE,qBACC,6LAAC;QAAI,WAAU;;0BAEd,6LAAC;gBAAI,WAAU;;kCACd,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAG1C,6LAAC;wBAAI,WAAU;;0CACd,6LAAC,0KAAI;gCACJ,MAAK;gCACL,WAAU;0CACV;;;;;;0CAGD,6LAAC,0KAAI;gCACJ,MAAK;gCACL,WAAU;0CACV;;;;;;;;;;;;;;;;;;0BAOH,6LAAC;gBAAI,WAAU;0BACd,cAAA,6LAAC;oBACA,MAAK;oBACL,aAAY;oBACZ,OAAO;oBACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;oBAC7C,WAAU;;;;;;;;;;;0BAKZ,6LAAC;;kCACA,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;oBAIrD,wBACA,6LAAC;wBAAI,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCAAgB;;;;;;;;;;+BAE7B,iBAAiB,MAAM,KAAK,kBAC/B,6LAAC;wBAAI,WAAU;;0CACd,6LAAC;gCAAI,WAAU;0CACb,aACE,4CACA;;;;;;0CAEJ,6LAAC,0KAAI;gCACJ,MAAK;gCACL,WAAU;0CACV;;;;;;;;;;;6CAKF,6LAAC;wBAAI,WAAU;kCACb,iBAAiB,GAAG,CAAC,CAAC,wBACtB,6LAAC;gCAEA,WAAU;;oCAET,QAAQ,SAAS,kBACjB,6LAAC;wCACA,KAAK,QAAQ,SAAS;wCACtB,KAAK,QAAQ,IAAI;wCACjB,WAAU;;;;;;kDAGZ,6LAAC;wCAAI,WAAU;;0DACd,6LAAC;gDAAG,WAAU;0DACZ,QAAQ,IAAI;;;;;;0DAEd,6LAAC;gDAAE,WAAU;0DACX,QAAQ,WAAW;;;;;;0DAErB,6LAAC;gDAAI,WAAU;;kEACd,6LAAC;wDAAK,WAAU;;4DAAmC;4DAChD,QAAQ,KAAK;;;;;;;kEAEhB,6LAAC,0KAAI;wDACJ,MAAM,AAAC,aAAuB,OAAX,QAAQ,EAAE;wDAC7B,WAAU;kEACV;;;;;;;;;;;;;;;;;;;+BAxBE,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;AAoCvB;GA/HwB;KAAA", "debugId": null}}]}