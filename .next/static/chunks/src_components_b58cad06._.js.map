{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/shreeji/marketplace/src/components/Logo.tsx"], "sourcesContent": ["'use client'\n\ninterface LogoProps {\n\tsize?: 'small' | 'medium' | 'large';\n\tclassName?: string;\n}\n\nexport default function ShreeRadhaLogo({ size = 'medium', className = '' }: LogoProps) {\n\tconst sizeClasses = {\n\t\tsmall: 'w-8 h-8 text-sm',\n\t\tmedium: 'w-12 h-12 text-base',\n\t\tlarge: 'w-16 h-16 text-lg'\n\t};\n\n\treturn (\n\t\t<div className={`flex items-center space-x-2 ${className}`}>\n\t\t\t{/* Logo Icon */}\n\t\t\t<div className={`${sizeClasses[size]} relative flex items-center justify-center`}>\n\t\t\t\t{/* Outer Circle with Gradient */}\n\t\t\t\t<div className=\"absolute inset-0 rounded-full bg-gradient-to-br from-orange-400 via-orange-500 to-red-500 shadow-lg\"></div>\n\t\t\t\t\n\t\t\t\t{/* Inner Circle */}\n\t\t\t\t<div className=\"absolute inset-1 rounded-full bg-gradient-to-br from-yellow-200 to-orange-200\"></div>\n\t\t\t\t\n\t\t\t\t{/* Sacred Symbol */}\n\t\t\t\t<div className=\"relative z-10 text-orange-800 font-bold sacred-text\">\n\t\t\t\t\t{size === 'small' ? '🕉️' : size === 'medium' ? '🕉️' : '🕉️'}\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t\t\n\t\t\t{/* Text Logo */}\n\t\t\t<div className=\"flex flex-col\">\n\t\t\t\t<div className={`font-bold text-orange-800 sacred-text leading-tight ${\n\t\t\t\t\tsize === 'small' ? 'text-sm' : size === 'medium' ? 'text-lg' : 'text-xl'\n\t\t\t\t}`}>\n\t\t\t\t\tश्री राधा नाम\n\t\t\t\t</div>\n\t\t\t\t<div className={`text-orange-600 font-medium ${\n\t\t\t\t\tsize === 'small' ? 'text-xs' : size === 'medium' ? 'text-sm' : 'text-base'\n\t\t\t\t}`}>\n\t\t\t\t\tShree Radha Nam\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</div>\n\t);\n}\n\n// Alternative text-only logo component\nexport function ShreeRadhaTextLogo({ size = 'medium', className = '' }: LogoProps) {\n\tconst sizeClasses = {\n\t\tsmall: 'text-lg',\n\t\tmedium: 'text-xl',\n\t\tlarge: 'text-2xl'\n\t};\n\n\treturn (\n\t\t<div className={`${className}`}>\n\t\t\t<div className={`font-bold text-white sacred-text ${sizeClasses[size]} flex items-center space-x-2`}>\n\t\t\t\t<span className=\"text-yellow-200\">🕉️</span>\n\t\t\t\t<span>श्री राधा नाम</span>\n\t\t\t\t<span className=\"text-yellow-200\">🕉️</span>\n\t\t\t</div>\n\t\t\t<div className=\"text-orange-100 text-sm font-medium text-center\">\n\t\t\t\tShree Radha Nam\n\t\t\t</div>\n\t\t</div>\n\t);\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAOe,SAAS,eAAe,KAA8C;QAA9C,EAAE,OAAO,QAAQ,EAAE,YAAY,EAAE,EAAa,GAA9C;IACtC,MAAM,cAAc;QACnB,OAAO;QACP,QAAQ;QACR,OAAO;IACR;IAEA,qBACC,6LAAC;QAAI,WAAW,AAAC,+BAAwC,OAAV;;0BAE9C,6LAAC;gBAAI,WAAW,AAAC,GAAoB,OAAlB,WAAW,CAAC,KAAK,EAAC;;kCAEpC,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;;;;;;kCAGf,6LAAC;wBAAI,WAAU;kCACb,SAAS,UAAU,QAAQ,SAAS,WAAW,QAAQ;;;;;;;;;;;;0BAK1D,6LAAC;gBAAI,WAAU;;kCACd,6LAAC;wBAAI,WAAW,AAAC,uDAEhB,OADA,SAAS,UAAU,YAAY,SAAS,WAAW,YAAY;kCAC5D;;;;;;kCAGJ,6LAAC;wBAAI,WAAW,AAAC,+BAEhB,OADA,SAAS,UAAU,YAAY,SAAS,WAAW,YAAY;kCAC5D;;;;;;;;;;;;;;;;;;AAMR;KAtCwB;AAyCjB,SAAS,mBAAmB,KAA8C;QAA9C,EAAE,OAAO,QAAQ,EAAE,YAAY,EAAE,EAAa,GAA9C;IAClC,MAAM,cAAc;QACnB,OAAO;QACP,QAAQ;QACR,OAAO;IACR;IAEA,qBACC,6LAAC;QAAI,WAAW,AAAC,GAAY,OAAV;;0BAClB,6LAAC;gBAAI,WAAW,AAAC,oCAAqD,OAAlB,WAAW,CAAC,KAAK,EAAC;;kCACrE,6LAAC;wBAAK,WAAU;kCAAkB;;;;;;kCAClC,6LAAC;kCAAK;;;;;;kCACN,6LAAC;wBAAK,WAAU;kCAAkB;;;;;;;;;;;;0BAEnC,6LAAC;gBAAI,WAAU;0BAAkD;;;;;;;;;;;;AAKpE;MAnBgB", "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/shreeji/marketplace/src/components/Navbar.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport { ShreeRadhaTextLogo } from \"./Logo\";\n\nexport default function Navbar() {\n\treturn (\n\t\t<nav className=\"spiritual-gradient shadow-lg border-b-2 border-orange-300 divine-glow\">\n\t\t\t<div className=\"container mx-auto px-4\">\n\t\t\t\t<div className=\"flex justify-between items-center h-16\">\n\t\t\t\t\t<Link href=\"/\" className=\"flex items-center\">\n\t\t\t\t\t\t<ShreeRadhaTextLogo size=\"medium\" />\n\t\t\t\t\t</Link>\n\n\t\t\t\t\t<div className=\"flex items-center space-x-6\">\n\t\t\t\t\t\t<Link\n\t\t\t\t\t\t\thref=\"/products\"\n\t\t\t\t\t\t\tclassName=\"text-white hover:text-yellow-200 font-semibold transition-colors duration-200\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t🛍️ Shop\n\t\t\t\t\t\t</Link>\n\t\t\t\t\t\t<Link\n\t\t\t\t\t\t\thref=\"/how-to-order\"\n\t\t\t\t\t\t\tclassName=\"text-white hover:text-yellow-200 font-semibold transition-colors duration-200\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t❓ How to Order\n\t\t\t\t\t\t</Link>\n\t\t\t\t\t\t<Link\n\t\t\t\t\t\t\thref=\"/orders\"\n\t\t\t\t\t\t\tclassName=\"text-white hover:text-yellow-200 font-semibold transition-colors duration-200\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t📋 Orders\n\t\t\t\t\t\t</Link>\n\t\t\t\t\t\t<Link\n\t\t\t\t\t\t\thref=\"/add-product\"\n\t\t\t\t\t\t\tclassName=\"text-orange-100 hover:text-yellow-200 text-sm font-medium transition-colors duration-200\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t⚙️ Admin\n\t\t\t\t\t\t</Link>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</nav>\n\t);\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACvB,qBACC,6LAAC;QAAI,WAAU;kBACd,cAAA,6LAAC;YAAI,WAAU;sBACd,cAAA,6LAAC;gBAAI,WAAU;;kCACd,6LAAC,0KAAI;wBAAC,MAAK;wBAAI,WAAU;kCACxB,cAAA,6LAAC,mJAAkB;4BAAC,MAAK;;;;;;;;;;;kCAG1B,6LAAC;wBAAI,WAAU;;0CACd,6LAAC,0KAAI;gCACJ,MAAK;gCACL,WAAU;0CACV;;;;;;0CAGD,6LAAC,0KAAI;gCACJ,MAAK;gCACL,WAAU;0CACV;;;;;;0CAGD,6LAAC,0KAAI;gCACJ,MAAK;gCACL,WAAU;0CACV;;;;;;0CAGD,6LAAC,0KAAI;gCACJ,MAAK;gCACL,WAAU;0CACV;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQP;KAvCwB", "debugId": null}}]}