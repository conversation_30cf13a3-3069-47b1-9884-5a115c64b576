"use client";

import { useState, useEffect } from "react";
import { getProducts, Product } from "@/lib/supabase";
import Link from "next/link";

export default function Home() {
	const [products, setProducts] = useState<Product[]>([]);
	const [loading, setLoading] = useState(true);
	const [searchTerm, setSearchTerm] = useState("");

	useEffect(() => {
		fetchProducts();
	}, []);

	const fetchProducts = () => {
		try {
			const allProducts = getProducts();
			setProducts(allProducts);
		} catch (error) {
			console.error("Error fetching products:", error);
		} finally {
			setLoading(false);
		}
	};

	const filteredProducts = products.filter(
		(product) =>
			product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
			product.description?.toLowerCase().includes(searchTerm.toLowerCase())
	);

	return (
		<div className="space-y-8">
			{/* Hero Section */}
			<div className="text-center py-16 bg-gradient-to-br from-orange-50 to-yellow-50 rounded-2xl shadow-xl border-2 border-orange-200 divine-glow">
				<div className="mb-6">
					<h1 className="text-5xl font-bold text-orange-800 mb-4 sacred-text">
						🙏 Welcome to Shri Hit Store 🙏
					</h1>
					<p className="text-xl text-orange-700 mb-2">
						Divine Spiritual Items from Sacred Vrindavan
					</p>
					<p className="text-lg text-orange-600">
						✨ For Our Beloved Bhakts ✨
					</p>
				</div>

				<div className="flex justify-center space-x-4">
					<Link
						href="/products"
						className="spiritual-gradient text-white px-8 py-4 rounded-full hover:scale-105 transform transition-all duration-300 font-bold text-lg shadow-lg divine-glow"
					>
						🛍️ Shop Sacred Items
					</Link>
				</div>

				{/* WhatsApp Info */}
				<div className="mt-8 text-center">
					<div className="bg-gradient-to-r from-green-100 to-green-50 border-2 border-green-300 rounded-xl p-6 max-w-2xl mx-auto">
						<h3 className="text-lg font-bold text-green-800 mb-2">
							📱 Easy WhatsApp Ordering
						</h3>
						<p className="text-green-700 mb-3">
							No online payments needed! Browse items, fill details, and
							complete your order via WhatsApp.
						</p>
						<Link
							href="/how-to-order"
							className="text-green-600 hover:text-green-500 font-semibold underline"
						>
							Learn how it works →
						</Link>
					</div>
				</div>
			</div>

			{/* Search */}
			<div className="max-w-md mx-auto">
				<div className="relative">
					<input
						type="text"
						placeholder="🔍 Search spiritual items..."
						value={searchTerm}
						onChange={(e) => setSearchTerm(e.target.value)}
						className="w-full px-6 py-3 border-2 border-orange-300 rounded-full focus:outline-none focus:ring-2 focus:ring-orange-500 text-orange-900 bg-white shadow-lg font-medium"
					/>
				</div>
			</div>

			{/* Our Products */}
			<div>
				<h2 className="text-3xl font-bold text-orange-800 mb-8 text-center sacred-text">
					🕉️ Our Sacred Collection 🕉️
				</h2>

				{loading ? (
					<div className="text-center py-12">
						<div className="text-orange-600 text-lg">
							🙏 Loading sacred items...
						</div>
					</div>
				) : filteredProducts.length === 0 ? (
					<div className="text-center py-12">
						<div className="text-orange-600 text-lg">
							{searchTerm
								? "🔍 No sacred items found matching your search."
								: "📿 No sacred items available yet."}
						</div>
					</div>
				) : (
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
						{filteredProducts.map((product) => (
							<div
								key={product.id}
								className="bg-gradient-to-br from-white to-orange-50 rounded-2xl shadow-lg border-2 border-orange-200 hover:shadow-xl transition-all duration-300 hover:scale-105 divine-glow"
							>
								{product.image_url && (
									<img
										src={product.image_url}
										alt={product.name}
										className="w-full h-48 object-cover rounded-t-2xl"
									/>
								)}
								<div className="p-6">
									<h3 className="font-bold text-orange-800 mb-3 text-lg sacred-text">
										{product.name}
									</h3>
									<p className="text-orange-700 text-sm mb-4 line-clamp-2">
										{product.description}
									</p>
									<div className="flex justify-between items-center">
										<span className="text-xl font-bold text-orange-600">
											₹{product.price}
										</span>
										<Link
											href={`/products/${product.id}`}
											className="spiritual-gradient text-white px-4 py-2 rounded-full text-sm hover:scale-105 transform transition-all duration-200 font-semibold shadow-md"
										>
											🛍️ View
										</Link>
									</div>
								</div>
							</div>
						))}
					</div>
				)}
			</div>
		</div>
	);
}
